/* 《能量星球》全局样式 */

/* 引入样式系统 */
@import "styles/variables.wxss";

/* 重置按钮样式 */
button {
  background: initial;
  border: none;
  outline: none;
}

button:focus {
  outline: 0;
}

button::after {
  border: none;
}

/* 全局页面样式 */
page {
  background: linear-gradient(135deg, #1A183E 0%, #2D1B69 100%);
  color: #FFFFFF;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', sans-serif;
  min-height: 100vh;
}

/* 基础布局 */
.container {
  width: 100%;
  padding: 0 32rpx;
  margin: 0 auto;
}

.page {
  min-height: 100vh;
  background: linear-gradient(135deg, #1A183E 0%, #2D1B69 100%);
}

/* Flex布局 */
.flex {
  display: flex;
}

.flex-column {
  display: flex;
  flex-direction: column;
}

.flex-center {
  display: flex;
  align-items: center;
  justify-content: center;
}

.flex-between {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.flex-1 {
  flex: 1;
}

/* 文字样式 */
.text-center {
  text-align: center;
}

.title-xxl {
  font-size: 64rpx;
  font-weight: 700;
  line-height: 1.2;
  color: #FFD76A;
}

.subtitle {
  font-size: 32rpx;
  color: #FFFFFF;
  opacity: 0.8;
  line-height: 1.4;
}