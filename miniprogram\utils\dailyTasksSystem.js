// 《能量星球》今日任务管理系统
// 专注于现实世界行为管理和品格培养

const dailyTasksSystem = {
  
  // 任务状态枚举
  TASK_STATUS: {
    PENDING: 'pending',           // 待完成
    IN_PROGRESS: 'in_progress',   // 进行中
    COMPLETED: 'completed',       // 已完成
    CONFIRMED: 'confirmed',       // 家长已确认
    EXPIRED: 'expired'            // 已过期
  },

  // 任务类型枚举
  TASK_TYPES: {
    LIFE_MANAGEMENT: 'life_management',     // 生活管理
    FAMILY_CARE: 'family_care',             // 家庭关爱
    SOCIAL_GROWTH: 'social_growth',         // 社交成长
    SOCIAL_PARTICIPATION: 'social_participation' // 社会参与
  },

  // 能量类型
  ENERGY_TYPES: {
    LOVE: 'love',       // 爱心能量 ❤️
    WISDOM: 'wisdom'    // 智慧能量 🔷
  },

  // 生成今日任务
  generateDailyTasks(userData = {}) {
    const age = userData.age || 8; // 默认8岁
    const interests = userData.interests || [];
    const weakPoints = userData.weakPoints || [];
    
    return {
      lifeTasks: this.generateLifeTasks(age, interests, weakPoints),
      familyTasks: this.generateFamilyTasks(age, interests, weakPoints),
      socialTasks: this.generateSocialTasks(age, interests, weakPoints),
      participationTasks: this.generateParticipationTasks(age, interests, weakPoints)
    };
  },

  // 生成生活管理任务
  generateLifeTasks(age, interests, weakPoints) {
    const baseTasks = [
      {
        id: 'life_001',
        name: '按时刷牙洗脸',
        description: '早晚各一次，保持口腔和面部清洁',
        category: this.TASK_TYPES.LIFE_MANAGEMENT,
        energyReward: { type: this.ENERGY_TYPES.LOVE, amount: 5 },
        requiresParentConfirm: true,
        ageRange: [3, 12],
        difficulty: 1,
        habitType: 'hygiene'
      },
      {
        id: 'life_002',
        name: '整理自己的房间',
        description: '收拾玩具、整理书桌、叠好被子',
        category: this.TASK_TYPES.LIFE_MANAGEMENT,
        energyReward: { type: this.ENERGY_TYPES.LOVE, amount: 10 },
        requiresParentConfirm: true,
        ageRange: [4, 12],
        difficulty: 2,
        habitType: 'organization'
      },
      {
        id: 'life_003',
        name: '按时起床不赖床',
        description: '听到闹钟后5分钟内起床',
        category: this.TASK_TYPES.LIFE_MANAGEMENT,
        energyReward: { type: this.ENERGY_TYPES.WISDOM, amount: 8 },
        requiresParentConfirm: true,
        ageRange: [5, 12],
        difficulty: 2,
        habitType: 'time_management'
      },
      {
        id: 'life_004',
        name: '整理书包和学习用品',
        description: '检查明天需要的课本和文具',
        category: this.TASK_TYPES.LIFE_MANAGEMENT,
        energyReward: { type: this.ENERGY_TYPES.WISDOM, amount: 8 },
        requiresParentConfirm: false,
        ageRange: [6, 12],
        difficulty: 2,
        habitType: 'organization'
      }
    ];

    return this.filterTasksByAge(baseTasks, age).slice(0, 3).map(task => ({
      ...task,
      status: this.TASK_STATUS.PENDING,
      createdAt: new Date().toISOString(),
      completedAt: null
    }));
  },

  // 生成家庭关爱任务
  generateFamilyTasks(age, interests, weakPoints) {
    const baseTasks = [
      {
        id: 'family_001',
        name: '帮助父母做家务',
        description: '扫地、擦桌子、洗碗等力所能及的家务',
        category: this.TASK_TYPES.FAMILY_CARE,
        energyReward: { type: this.ENERGY_TYPES.LOVE, amount: 15 },
        requiresParentConfirm: true,
        ageRange: [4, 12],
        difficulty: 2,
        habitType: 'helping'
      },
      {
        id: 'family_002',
        name: '向家人表达感谢',
        description: '对家人说"谢谢"或给一个拥抱',
        category: this.TASK_TYPES.FAMILY_CARE,
        energyReward: { type: this.ENERGY_TYPES.LOVE, amount: 10 },
        requiresParentConfirm: false,
        ageRange: [3, 12],
        difficulty: 1,
        habitType: 'gratitude'
      },
      {
        id: 'family_003',
        name: '分享今天的收获',
        description: '和家人分享今天学到的新知识或有趣的事',
        category: this.TASK_TYPES.FAMILY_CARE,
        energyReward: { type: this.ENERGY_TYPES.LOVE, amount: 12 },
        requiresParentConfirm: false,
        ageRange: [5, 12],
        difficulty: 2,
        habitType: 'communication'
      },
      {
        id: 'family_004',
        name: '照顾弟弟妹妹或宠物',
        description: '帮助照顾家里的小朋友或宠物',
        category: this.TASK_TYPES.FAMILY_CARE,
        energyReward: { type: this.ENERGY_TYPES.LOVE, amount: 18 },
        requiresParentConfirm: true,
        ageRange: [6, 12],
        difficulty: 3,
        habitType: 'caring'
      }
    ];

    return this.filterTasksByAge(baseTasks, age).slice(0, 3).map(task => ({
      ...task,
      status: this.TASK_STATUS.PENDING,
      createdAt: new Date().toISOString(),
      completedAt: null
    }));
  },

  // 生成社交成长任务
  generateSocialTasks(age, interests, weakPoints) {
    const baseTasks = [
      {
        id: 'social_001',
        name: '主动问候同学朋友',
        description: '见到同学朋友时主动打招呼',
        category: this.TASK_TYPES.SOCIAL_GROWTH,
        energyReward: { type: this.ENERGY_TYPES.LOVE, amount: 8 },
        requiresParentConfirm: false,
        ageRange: [4, 12],
        difficulty: 1,
        habitType: 'politeness'
      },
      {
        id: 'social_002',
        name: '使用礼貌用语',
        description: '说"请"、"谢谢"、"对不起"等礼貌用语',
        category: this.TASK_TYPES.SOCIAL_GROWTH,
        energyReward: { type: this.ENERGY_TYPES.LOVE, amount: 6 },
        requiresParentConfirm: false,
        ageRange: [3, 12],
        difficulty: 1,
        habitType: 'politeness'
      },
      {
        id: 'social_003',
        name: '帮助需要帮助的同学',
        description: '主动帮助遇到困难的同学',
        category: this.TASK_TYPES.SOCIAL_GROWTH,
        energyReward: { type: this.ENERGY_TYPES.LOVE, amount: 15 },
        requiresParentConfirm: false,
        ageRange: [5, 12],
        difficulty: 2,
        habitType: 'helping'
      },
      {
        id: 'social_004',
        name: '参与集体活动',
        description: '积极参加班级或小组活动',
        category: this.TASK_TYPES.SOCIAL_GROWTH,
        energyReward: { type: this.ENERGY_TYPES.LOVE, amount: 12 },
        requiresParentConfirm: false,
        ageRange: [6, 12],
        difficulty: 2,
        habitType: 'cooperation'
      }
    ];

    return this.filterTasksByAge(baseTasks, age).slice(0, 3).map(task => ({
      ...task,
      status: this.TASK_STATUS.PENDING,
      createdAt: new Date().toISOString(),
      completedAt: null
    }));
  },

  // 生成社会参与任务
  generateParticipationTasks(age, interests, weakPoints) {
    const baseTasks = [
      {
        id: 'participation_001',
        name: '垃圾分类投放',
        description: '正确分类投放垃圾，保护环境',
        category: this.TASK_TYPES.SOCIAL_PARTICIPATION,
        energyReward: { type: this.ENERGY_TYPES.LOVE, amount: 10 },
        requiresParentConfirm: true,
        ageRange: [4, 12],
        difficulty: 1,
        habitType: 'environmental'
      },
      {
        id: 'participation_002',
        name: '节约用水用电',
        description: '随手关灯、关水龙头，节约资源',
        category: this.TASK_TYPES.SOCIAL_PARTICIPATION,
        energyReward: { type: this.ENERGY_TYPES.LOVE, amount: 8 },
        requiresParentConfirm: true,
        ageRange: [5, 12],
        difficulty: 1,
        habitType: 'environmental'
      },
      {
        id: 'participation_003',
        name: '爱护公共设施',
        description: '不破坏公园、学校等公共场所的设施',
        category: this.TASK_TYPES.SOCIAL_PARTICIPATION,
        energyReward: { type: this.ENERGY_TYPES.LOVE, amount: 12 },
        requiresParentConfirm: false,
        ageRange: [6, 12],
        difficulty: 2,
        habitType: 'civic_responsibility'
      },
      {
        id: 'participation_004',
        name: '了解传统文化',
        description: '学习一个传统节日或民俗知识',
        category: this.TASK_TYPES.SOCIAL_PARTICIPATION,
        energyReward: { type: this.ENERGY_TYPES.WISDOM, amount: 15 },
        requiresParentConfirm: false,
        ageRange: [7, 12],
        difficulty: 3,
        habitType: 'cultural'
      }
    ];

    return this.filterTasksByAge(baseTasks, age).slice(0, 3).map(task => ({
      ...task,
      status: this.TASK_STATUS.PENDING,
      createdAt: new Date().toISOString(),
      completedAt: null
    }));
  },

  // 根据年龄过滤任务
  filterTasksByAge(tasks, age) {
    return tasks.filter(task => age >= task.ageRange[0] && age <= task.ageRange[1]);
  },

  // 完成任务
  completeTask(taskId) {
    const today = new Date().toDateString();
    const todayTasks = wx.getStorageSync(`dailyTasks_${today}`) || {};
    
    // 查找并更新任务状态
    let taskFound = false;
    Object.keys(todayTasks).forEach(category => {
      if (Array.isArray(todayTasks[category])) {
        todayTasks[category].forEach(task => {
          if (task.id === taskId) {
            task.status = this.TASK_STATUS.COMPLETED;
            task.completedAt = new Date().toISOString();
            taskFound = true;
            
            // 添加能量奖励
            this.addEnergyReward(task.energyReward);
          }
        });
      }
    });
    
    if (taskFound) {
      wx.setStorageSync(`dailyTasks_${today}`, todayTasks);
      return true;
    }
    return false;
  },

  // 添加能量奖励
  addEnergyReward(energyReward) {
    const today = new Date().toDateString();
    const todayEnergyData = wx.getStorageSync(`energy_${today}`) || {
      loveEnergy: 0,
      wisdomEnergy: 0
    };
    
    if (energyReward.type === this.ENERGY_TYPES.LOVE) {
      todayEnergyData.loveEnergy += energyReward.amount;
    } else if (energyReward.type === this.ENERGY_TYPES.WISDOM) {
      todayEnergyData.wisdomEnergy += energyReward.amount;
    }
    
    wx.setStorageSync(`energy_${today}`, todayEnergyData);
    
    // 同时更新总能量
    const userData = wx.getStorageSync('userData') || {};
    if (energyReward.type === this.ENERGY_TYPES.LOVE) {
      userData.loveEnergy = (userData.loveEnergy || 0) + energyReward.amount;
    } else if (energyReward.type === this.ENERGY_TYPES.WISDOM) {
      userData.wisdomEnergy = (userData.wisdomEnergy || 0) + energyReward.amount;
    }
    wx.setStorageSync('userData', userData);
  },

  // 获取今日任务统计
  getTodayStats() {
    const today = new Date().toDateString();
    const todayTasks = wx.getStorageSync(`dailyTasks_${today}`) || {};
    
    let totalTasks = 0;
    let completedTasks = 0;
    
    Object.keys(todayTasks).forEach(category => {
      if (Array.isArray(todayTasks[category])) {
        totalTasks += todayTasks[category].length;
        completedTasks += todayTasks[category].filter(task => 
          task.status === this.TASK_STATUS.COMPLETED || 
          task.status === this.TASK_STATUS.CONFIRMED
        ).length;
      }
    });
    
    return {
      totalTasks,
      completedTasks,
      completionRate: totalTasks > 0 ? Math.round((completedTasks / totalTasks) * 100) : 0
    };
  }
};

module.exports = {
  dailyTasksSystem
};
