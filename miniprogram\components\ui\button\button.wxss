/* 能量星球按钮组件样式 */

.btn {
  position: relative;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: 8rpx;
  min-height: 80rpx;
  min-width: 60rpx;
  padding: 16rpx 32rpx;
  border: none;
  border-radius: 24rpx;
  font-size: 32rpx;
  font-weight: 600;
  text-align: center;
  transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  overflow: hidden;
  box-sizing: border-box;
}

.btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(255, 255, 255, 0.1);
  opacity: 0;
  transition: opacity 0.15s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

.btn:active::before {
  opacity: 1;
}

.btn:active {
  transform: scale(0.95);
}

/* 按钮类型样式 */
.btn-primary {
  background: linear-gradient(135deg, #FFD76A 0%, #FFE066 100%);
  color: #1A183E;
  box-shadow: 0 8rpx 24rpx rgba(255, 215, 106, 0.4);
}

.btn-secondary {
  background: linear-gradient(135deg, #4D9FFF 0%, #66B3FF 100%);
  color: #FFFFFF;
  box-shadow: 0 8rpx 24rpx rgba(77, 159, 255, 0.4);
}

.btn-love {
  background: linear-gradient(135deg, #FF6B6B 0%, #FF8A8A 100%);
  color: #FFFFFF;
  box-shadow: 0 8rpx 24rpx rgba(255, 107, 107, 0.4);
}

.btn-success {
  background: linear-gradient(135deg, #63E2B7 0%, #7EEBC7 100%);
  color: #1A183E;
  box-shadow: 0 8rpx 24rpx rgba(99, 226, 183, 0.4);
}

.btn-ghost {
  background: transparent;
  color: #FFFFFF;
  border: 2rpx solid #FFFFFF;
  box-shadow: none;
}

.btn-ghost:hover {
  background: rgba(255, 255, 255, 0.1);
}

/* 按钮尺寸 */
.btn-sm {
  min-height: 60rpx;
  padding: 8rpx 16rpx;
  font-size: 28rpx;
}

.btn-lg {
  min-height: 100rpx;
  padding: 24rpx 48rpx;
  font-size: 36rpx;
}

/* 圆形按钮 */
.btn-round {
  border-radius: 50%;
  width: 60rpx;
  height: 60rpx;
  padding: 0;
}

/* 禁用状态 */
.btn-disabled {
  opacity: 0.5;
  transform: none !important;
  box-shadow: none !important;
}

/* 按钮内容 */
.btn-icon {
  display: flex;
  align-items: center;
  justify-content: center;
}

.btn-text {
  white-space: nowrap;
}

/* 加载状态 */
.btn-loading {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}

.loading-spinner {
  width: 32rpx;
  height: 32rpx;
  border: 4rpx solid rgba(255, 255, 255, 0.3);
  border-top: 4rpx solid #FFFFFF;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* 图标样式 */
.icon {
  display: block;
  width: 32rpx;
  height: 32rpx;
}

.icon-xs { width: 24rpx; height: 24rpx; }
.icon-sm { width: 32rpx; height: 32rpx; }
.icon-md { width: 48rpx; height: 48rpx; }
.icon-lg { width: 64rpx; height: 64rpx; }
