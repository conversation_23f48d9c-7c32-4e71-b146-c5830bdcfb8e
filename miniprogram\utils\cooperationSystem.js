// 《能量星球》亲子协作任务系统

/**
 * 亲子协作任务管理类
 */
class CooperationSystem {
  constructor() {
    this.storageKey = 'cooperationTasks';
    this.badgeKey = 'cooperationBadges';
    this.defaultTasks = this.getDefaultTasks();
  }

  /**
   * 获取默认任务模板
   */
  getDefaultTasks() {
    return [
      {
        id: 'coop_001',
        title: '一起阅读探险',
        description: '和孩子一起阅读30分钟，分享故事中的精彩片段',
        type: 'reading',
        duration: 30, // 分钟
        energyReward: {
          wisdom: 20,
          love: 30
        },
        badge: {
          name: '阅读探险家',
          icon: '📚',
          description: '完成首次亲子阅读任务'
        },
        difficulty: 'easy',
        category: '学习成长'
      },
      {
        id: 'coop_002',
        title: '周末公园探索',
        description: '周末带孩子去公园，观察自然，收集有趣的发现',
        type: 'outdoor',
        duration: 120, // 分钟
        energyReward: {
          wisdom: 15,
          love: 40
        },
        badge: {
          name: '自然探索者',
          icon: '🌳',
          description: '完成户外探索任务'
        },
        difficulty: 'medium',
        category: '户外活动'
      },
      {
        id: 'coop_003',
        title: '创意手工制作',
        description: '一起制作手工艺品，发挥创造力',
        type: 'creative',
        duration: 60,
        energyReward: {
          wisdom: 25,
          love: 25
        },
        badge: {
          name: '创意大师',
          icon: '🎨',
          description: '完成创意制作任务'
        },
        difficulty: 'medium',
        category: '创意表达'
      },
      {
        id: 'coop_004',
        title: '厨房小助手',
        description: '教孩子简单的烹饪技能，一起准备美食',
        type: 'life_skill',
        duration: 45,
        energyReward: {
          wisdom: 20,
          love: 35
        },
        badge: {
          name: '小厨师',
          icon: '👨‍🍳',
          description: '掌握基础烹饪技能'
        },
        difficulty: 'medium',
        category: '生活技能'
      },
      {
        id: 'coop_005',
        title: '科学小实验',
        description: '进行简单有趣的科学实验，探索科学奥秘',
        type: 'science',
        duration: 40,
        energyReward: {
          wisdom: 35,
          love: 20
        },
        badge: {
          name: '小科学家',
          icon: '🔬',
          description: '完成科学实验探索'
        },
        difficulty: 'hard',
        category: '科学探索'
      }
    ];
  }

  /**
   * 获取所有任务
   */
  getAllTasks() {
    const tasks = wx.getStorageSync(this.storageKey);
    if (!tasks || tasks.length === 0) {
      const initialTasks = this.defaultTasks.map(task => ({
        ...task,
        status: 'available', // available, active, completed
        createdAt: Date.now(),
        startedAt: null,
        completedAt: null,
        parentConfirmed: false,
        childConfirmed: false
      }));
      this.saveTasks(initialTasks);
      return initialTasks;
    }
    return tasks;
  }

  /**
   * 保存任务数据
   */
  saveTasks(tasks) {
    wx.setStorageSync(this.storageKey, tasks);
  }

  /**
   * 创建自定义任务
   */
  createCustomTask(taskData) {
    const tasks = this.getAllTasks();
    const newTask = {
      id: `custom_${Date.now()}`,
      ...taskData,
      status: 'available',
      createdAt: Date.now(),
      startedAt: null,
      completedAt: null,
      parentConfirmed: false,
      childConfirmed: false,
      isCustom: true
    };
    
    tasks.push(newTask);
    this.saveTasks(tasks);
    return newTask;
  }

  /**
   * 开始任务
   */
  startTask(taskId) {
    const tasks = this.getAllTasks();
    const taskIndex = tasks.findIndex(t => t.id === taskId);
    
    if (taskIndex !== -1 && tasks[taskIndex].status === 'available') {
      tasks[taskIndex].status = 'active';
      tasks[taskIndex].startedAt = Date.now();
      this.saveTasks(tasks);
      return { success: true, task: tasks[taskIndex] };
    }
    
    return { success: false, message: '任务无法开始' };
  }

  /**
   * 家长确认任务完成
   */
  parentConfirmTask(taskId) {
    const tasks = this.getAllTasks();
    const taskIndex = tasks.findIndex(t => t.id === taskId);
    
    if (taskIndex !== -1 && tasks[taskIndex].status === 'active') {
      tasks[taskIndex].parentConfirmed = true;
      
      // 如果双方都确认，则完成任务
      if (tasks[taskIndex].childConfirmed) {
        return this.completeTask(taskId);
      }
      
      this.saveTasks(tasks);
      return { 
        success: true, 
        message: '等待孩子确认完成',
        task: tasks[taskIndex]
      };
    }
    
    return { success: false, message: '任务状态异常' };
  }

  /**
   * 孩子确认任务完成（模拟）
   */
  childConfirmTask(taskId) {
    const tasks = this.getAllTasks();
    const taskIndex = tasks.findIndex(t => t.id === taskId);
    
    if (taskIndex !== -1 && tasks[taskIndex].status === 'active') {
      tasks[taskIndex].childConfirmed = true;
      
      // 如果双方都确认，则完成任务
      if (tasks[taskIndex].parentConfirmed) {
        return this.completeTask(taskId);
      }
      
      this.saveTasks(tasks);
      return { 
        success: true, 
        message: '等待家长确认完成',
        task: tasks[taskIndex]
      };
    }
    
    return { success: false, message: '任务状态异常' };
  }

  /**
   * 完成任务
   */
  completeTask(taskId) {
    const tasks = this.getAllTasks();
    const taskIndex = tasks.findIndex(t => t.id === taskId);
    
    if (taskIndex !== -1) {
      const task = tasks[taskIndex];
      task.status = 'completed';
      task.completedAt = Date.now();
      
      // 发放奖励
      this.grantRewards(task);
      
      // 颁发徽章
      this.awardBadge(task);
      
      this.saveTasks(tasks);
      
      return { 
        success: true, 
        message: '任务完成！获得奖励和徽章',
        task: task,
        rewards: task.energyReward,
        badge: task.badge
      };
    }
    
    return { success: false, message: '任务不存在' };
  }

  /**
   * 发放能量奖励
   */
  grantRewards(task) {
    // 获取当前用户数据
    const userData = wx.getStorageSync('userData') || {};
    
    // 增加能量
    userData.wisdomEnergy = (userData.wisdomEnergy || 0) + (task.energyReward.wisdom || 0);
    userData.loveEnergy = (userData.loveEnergy || 0) + (task.energyReward.love || 0);
    
    // 保存更新后的数据
    wx.setStorageSync('userData', userData);
  }

  /**
   * 颁发徽章
   */
  awardBadge(task) {
    if (!task.badge) return;
    
    const badges = wx.getStorageSync(this.badgeKey) || [];
    const newBadge = {
      ...task.badge,
      taskId: task.id,
      awardedAt: Date.now(),
      category: 'cooperation'
    };
    
    badges.push(newBadge);
    wx.setStorageSync(this.badgeKey, badges);
  }

  /**
   * 获取任务统计
   */
  getTaskStats() {
    const tasks = this.getAllTasks();
    
    return {
      total: tasks.length,
      available: tasks.filter(t => t.status === 'available').length,
      active: tasks.filter(t => t.status === 'active').length,
      completed: tasks.filter(t => t.status === 'completed').length,
      completionRate: tasks.length > 0 ? 
        Math.round((tasks.filter(t => t.status === 'completed').length / tasks.length) * 100) : 0
    };
  }

  /**
   * 获取推荐任务
   */
  getRecommendedTasks(childAge = 6, interests = []) {
    const allTasks = this.getAllTasks();
    const availableTasks = allTasks.filter(t => t.status === 'available');
    
    // 基于年龄和兴趣推荐任务
    return availableTasks
      .filter(task => {
        // 简单的年龄适配逻辑
        if (childAge <= 4 && task.difficulty === 'hard') return false;
        if (childAge >= 8 && task.difficulty === 'easy') return false;
        return true;
      })
      .slice(0, 3); // 返回前3个推荐任务
  }

  /**
   * 获取协作徽章
   */
  getCooperationBadges() {
    return wx.getStorageSync(this.badgeKey) || [];
  }

  /**
   * 重置任务（用于测试）
   */
  resetTasks() {
    wx.removeStorageSync(this.storageKey);
    wx.removeStorageSync(this.badgeKey);
  }
}

// 导出单例
const cooperationSystem = new CooperationSystem();

module.exports = {
  cooperationSystem
};
