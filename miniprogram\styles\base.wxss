/* 《能量星球》基础样式 */

/* 引入变量系统 */
@import "variables.wxss";

/* ==================== 全局重置 ==================== */

/* 页面基础设置 */
page {
  background: var(--gradient-space);
  color: var(--star-white);
  font-size: var(--font-size-md);
  line-height: var(--line-height-normal);
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* 通用重置 - 微信小程序兼容版本 */
view, text, button, image, input, textarea, scroll-view, swiper, swiper-item {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

/* ==================== 布局基础 ==================== */

/* 容器 */
.container {
  width: 100%;
  padding: 0 var(--page-padding-horizontal);
  margin: 0 auto;
}

/* 页面容器 */
.page {
  min-height: 100vh;
  padding-top: var(--safe-area-inset-top);
  padding-bottom: var(--safe-area-inset-bottom);
  background: var(--gradient-space);
}

/* Flex布局工具类 */
.flex {
  display: flex;
}

.flex-column {
  display: flex;
  flex-direction: column;
}

.flex-center {
  display: flex;
  align-items: center;
  justify-content: center;
}

.flex-between {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.flex-around {
  display: flex;
  align-items: center;
  justify-content: space-around;
}

.flex-wrap {
  flex-wrap: wrap;
}

.flex-1 {
  flex: 1;
}

/* 对齐工具类 */
.text-center {
  text-align: center;
}

.text-left {
  text-align: left;
}

.text-right {
  text-align: right;
}

/* ==================== 字体样式 ==================== */

/* 标题样式 */
.title-xxl {
  font-size: var(--font-size-xxl);
  font-weight: var(--font-weight-bold);
  line-height: var(--line-height-tight);
  color: var(--energy-yellow);
}

.title-xl {
  font-size: var(--font-size-xl);
  font-weight: var(--font-weight-bold);
  line-height: var(--line-height-tight);
  color: var(--star-white);
}

.title-lg {
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-semibold);
  line-height: var(--line-height-normal);
  color: var(--star-white);
}

.title-md {
  font-size: var(--font-size-md);
  font-weight: var(--font-weight-medium);
  line-height: var(--line-height-normal);
  color: var(--star-white);
}

/* 正文样式 */
.text-body {
  font-size: var(--font-size-md);
  font-weight: var(--font-weight-normal);
  line-height: var(--line-height-relaxed);
  color: var(--star-white);
}

.text-small {
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-normal);
  line-height: var(--line-height-normal);
  color: var(--neutral-gray);
}

.text-caption {
  font-size: var(--font-size-xs);
  font-weight: var(--font-weight-normal);
  line-height: var(--line-height-normal);
  color: var(--neutral-gray);
}

/* 强调文字 */
.text-energy {
  color: var(--energy-yellow);
}

.text-wisdom {
  color: var(--wisdom-blue);
}

.text-love {
  color: var(--love-red);
}

.text-success {
  color: var(--success-green);
}

/* ==================== 间距工具类 ==================== */

/* 外边距 */
.m-0 { margin: 0; }
.m-xs { margin: var(--spacing-xs); }
.m-sm { margin: var(--spacing-sm); }
.m-md { margin: var(--spacing-md); }
.m-lg { margin: var(--spacing-lg); }
.m-xl { margin: var(--spacing-xl); }

/* 垂直外边距 */
.my-0 { margin-top: 0; margin-bottom: 0; }
.my-xs { margin-top: var(--spacing-xs); margin-bottom: var(--spacing-xs); }
.my-sm { margin-top: var(--spacing-sm); margin-bottom: var(--spacing-sm); }
.my-md { margin-top: var(--spacing-md); margin-bottom: var(--spacing-md); }
.my-lg { margin-top: var(--spacing-lg); margin-bottom: var(--spacing-lg); }
.my-xl { margin-top: var(--spacing-xl); margin-bottom: var(--spacing-xl); }

/* 水平外边距 */
.mx-0 { margin-left: 0; margin-right: 0; }
.mx-xs { margin-left: var(--spacing-xs); margin-right: var(--spacing-xs); }
.mx-sm { margin-left: var(--spacing-sm); margin-right: var(--spacing-sm); }
.mx-md { margin-left: var(--spacing-md); margin-right: var(--spacing-md); }
.mx-lg { margin-left: var(--spacing-lg); margin-right: var(--spacing-lg); }
.mx-xl { margin-left: var(--spacing-xl); margin-right: var(--spacing-xl); }

/* 单方向外边距 */
.mt-0 { margin-top: 0; }
.mt-xs { margin-top: var(--spacing-xs); }
.mt-sm { margin-top: var(--spacing-sm); }
.mt-md { margin-top: var(--spacing-md); }
.mt-lg { margin-top: var(--spacing-lg); }
.mt-xl { margin-top: var(--spacing-xl); }

.mb-0 { margin-bottom: 0; }
.mb-xs { margin-bottom: var(--spacing-xs); }
.mb-sm { margin-bottom: var(--spacing-sm); }
.mb-md { margin-bottom: var(--spacing-md); }
.mb-lg { margin-bottom: var(--spacing-lg); }
.mb-xl { margin-bottom: var(--spacing-xl); }

.ml-0 { margin-left: 0; }
.ml-xs { margin-left: var(--spacing-xs); }
.ml-sm { margin-left: var(--spacing-sm); }
.ml-md { margin-left: var(--spacing-md); }
.ml-lg { margin-left: var(--spacing-lg); }
.ml-xl { margin-left: var(--spacing-xl); }

.mr-0 { margin-right: 0; }
.mr-xs { margin-right: var(--spacing-xs); }
.mr-sm { margin-right: var(--spacing-sm); }
.mr-md { margin-right: var(--spacing-md); }
.mr-lg { margin-right: var(--spacing-lg); }
.mr-xl { margin-right: var(--spacing-xl); }

/* 内边距 */
.p-0 { padding: 0; }
.p-xs { padding: var(--spacing-xs); }
.p-sm { padding: var(--spacing-sm); }
.p-md { padding: var(--spacing-md); }
.p-lg { padding: var(--spacing-lg); }
.p-xl { padding: var(--spacing-xl); }

/* 垂直内边距 */
.py-0 { padding-top: 0; padding-bottom: 0; }
.py-xs { padding-top: var(--spacing-xs); padding-bottom: var(--spacing-xs); }
.py-sm { padding-top: var(--spacing-sm); padding-bottom: var(--spacing-sm); }
.py-md { padding-top: var(--spacing-md); padding-bottom: var(--spacing-md); }
.py-lg { padding-top: var(--spacing-lg); padding-bottom: var(--spacing-lg); }
.py-xl { padding-top: var(--spacing-xl); padding-bottom: var(--spacing-xl); }

/* 水平内边距 */
.px-0 { padding-left: 0; padding-right: 0; }
.px-xs { padding-left: var(--spacing-xs); padding-right: var(--spacing-xs); }
.px-sm { padding-left: var(--spacing-sm); padding-right: var(--spacing-sm); }
.px-md { padding-left: var(--spacing-md); padding-right: var(--spacing-md); }
.px-lg { padding-left: var(--spacing-lg); padding-right: var(--spacing-lg); }
.px-xl { padding-left: var(--spacing-xl); padding-right: var(--spacing-xl); }

/* ==================== 显示工具类 ==================== */

.hidden {
  display: none;
}

.visible {
  display: block;
}

.invisible {
  visibility: hidden;
}

/* ==================== 位置工具类 ==================== */

.relative {
  position: relative;
}

.absolute {
  position: absolute;
}

.fixed {
  position: fixed;
}

.sticky {
  position: sticky;
}

/* ==================== 溢出处理 ==================== */

.overflow-hidden {
  overflow: hidden;
}

.overflow-scroll {
  overflow: scroll;
}

.overflow-auto {
  overflow: auto;
}

/* 文字溢出 */
.text-ellipsis {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

/* ==================== 圆角工具类 ==================== */

.rounded-none { border-radius: 0; }
.rounded-xs { border-radius: var(--radius-xs); }
.rounded-sm { border-radius: var(--radius-sm); }
.rounded-md { border-radius: var(--radius-md); }
.rounded-lg { border-radius: var(--radius-lg); }
.rounded-xl { border-radius: var(--radius-xl); }
.rounded-full { border-radius: var(--radius-round); }
.rounded-pill { border-radius: var(--radius-pill); }
