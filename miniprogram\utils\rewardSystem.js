// 《能量星球》奖励系统工具函数

/**
 * 奖励系统管理类
 */
class RewardSystem {
  constructor() {
    this.storageKey = 'parentRewards';
    this.defaultRewards = this.getDefaultRewards();
  }

  /**
   * 获取默认奖励配置
   */
  getDefaultRewards() {
    return [
      {
        id: 'reward_001',
        name: '看一部电影',
        type: 'activity',
        description: '和爸爸妈妈一起看喜欢的电影',
        condition: {
          type: 'energy',
          target: 'wisdom',
          amount: 100
        },
        status: 'active',
        validUntil: this.getDateAfterDays(30),
        icon: '🎬',
        createdAt: Date.now()
      },
      {
        id: 'reward_002',
        name: '新玩具',
        type: 'item',
        description: '选择一个心仪的小玩具',
        condition: {
          type: 'energy',
          target: 'love',
          amount: 80
        },
        status: 'active',
        validUntil: this.getDateAfterDays(30),
        icon: '🧸',
        createdAt: Date.now()
      },
      {
        id: 'reward_003',
        name: '公园游玩',
        type: 'activity',
        description: '去公园玩一整个下午',
        condition: {
          type: 'study_time',
          amount: 300 // 5小时，单位分钟
        },
        status: 'active',
        validUntil: this.getDateAfterDays(30),
        icon: '🌳',
        createdAt: Date.now()
      },
      {
        id: 'reward_004',
        name: '晚睡特权',
        type: 'privilege',
        description: '周末可以晚睡30分钟',
        condition: {
          type: 'game_completion',
          amount: 10
        },
        status: 'active',
        validUntil: this.getDateAfterDays(30),
        icon: '🌙',
        createdAt: Date.now()
      },
      {
        id: 'reward_005',
        name: '选择晚餐',
        type: 'privilege',
        description: '可以选择今天的晚餐菜单',
        condition: {
          type: 'energy',
          target: 'both',
          amount: 50
        },
        status: 'active',
        validUntil: this.getDateAfterDays(30),
        icon: '🍽️',
        createdAt: Date.now()
      }
    ];
  }

  /**
   * 获取指定天数后的日期
   */
  getDateAfterDays(days) {
    const date = new Date();
    date.setDate(date.getDate() + days);
    return date.toISOString().split('T')[0];
  }

  /**
   * 获取所有奖励
   */
  getAllRewards() {
    const rewards = wx.getStorageSync(this.storageKey);
    if (!rewards || rewards.length === 0) {
      this.saveRewards(this.defaultRewards);
      return this.defaultRewards;
    }
    return rewards;
  }

  /**
   * 保存奖励数据
   */
  saveRewards(rewards) {
    wx.setStorageSync(this.storageKey, rewards);
  }

  /**
   * 添加新奖励
   */
  addReward(rewardData) {
    const rewards = this.getAllRewards();
    const newReward = {
      id: `reward_${Date.now()}`,
      ...rewardData,
      status: 'active',
      createdAt: Date.now()
    };
    
    rewards.push(newReward);
    this.saveRewards(rewards);
    return newReward;
  }

  /**
   * 更新奖励
   */
  updateReward(rewardId, updateData) {
    const rewards = this.getAllRewards();
    const index = rewards.findIndex(r => r.id === rewardId);
    
    if (index !== -1) {
      rewards[index] = { ...rewards[index], ...updateData };
      this.saveRewards(rewards);
      return rewards[index];
    }
    return null;
  }

  /**
   * 删除奖励
   */
  deleteReward(rewardId) {
    const rewards = this.getAllRewards();
    const filteredRewards = rewards.filter(r => r.id !== rewardId);
    this.saveRewards(filteredRewards);
    return filteredRewards;
  }

  /**
   * 检查奖励是否可兑换
   */
  checkRewardEligibility(rewardId, childData) {
    const rewards = this.getAllRewards();
    const reward = rewards.find(r => r.id === rewardId);
    
    if (!reward || reward.status !== 'active') {
      return { eligible: false, reason: '奖励不可用' };
    }

    // 检查有效期
    const today = new Date().toISOString().split('T')[0];
    if (reward.validUntil && reward.validUntil < today) {
      return { eligible: false, reason: '奖励已过期' };
    }

    // 检查条件
    return this.checkCondition(reward.condition, childData);
  }

  /**
   * 检查条件是否满足
   */
  checkCondition(condition, childData) {
    switch (condition.type) {
      case 'energy':
        if (condition.target === 'wisdom') {
          return {
            eligible: childData.wisdomEnergy >= condition.amount,
            reason: childData.wisdomEnergy >= condition.amount ? 
              '条件已满足' : `需要${condition.amount}智慧能量，当前${childData.wisdomEnergy}`
          };
        } else if (condition.target === 'love') {
          return {
            eligible: childData.loveEnergy >= condition.amount,
            reason: childData.loveEnergy >= condition.amount ? 
              '条件已满足' : `需要${condition.amount}爱心能量，当前${childData.loveEnergy}`
          };
        } else if (condition.target === 'both') {
          const total = childData.wisdomEnergy + childData.loveEnergy;
          return {
            eligible: total >= condition.amount,
            reason: total >= condition.amount ? 
              '条件已满足' : `需要总能量${condition.amount}，当前${total}`
          };
        }
        break;
      
      case 'study_time':
        const totalStudyTime = this.getTotalStudyTime();
        return {
          eligible: totalStudyTime >= condition.amount,
          reason: totalStudyTime >= condition.amount ? 
            '条件已满足' : `需要学习${condition.amount}分钟，当前${totalStudyTime}分钟`
        };
      
      case 'game_completion':
        const totalGames = this.getTotalGamesCompleted();
        return {
          eligible: totalGames >= condition.amount,
          reason: totalGames >= condition.amount ? 
            '条件已满足' : `需要完成${condition.amount}个游戏，当前${totalGames}个`
        };
      
      default:
        return { eligible: false, reason: '未知条件类型' };
    }
  }

  /**
   * 获取总学习时间
   */
  getTotalStudyTime() {
    // 这里应该从实际的学习记录中获取
    // 暂时返回模拟数据
    return 180; // 3小时
  }

  /**
   * 获取总游戏完成数
   */
  getTotalGamesCompleted() {
    // 这里应该从实际的游戏记录中获取
    // 暂时返回模拟数据
    return 15;
  }

  /**
   * 兑换奖励
   */
  redeemReward(rewardId, childData) {
    const eligibility = this.checkRewardEligibility(rewardId, childData);
    
    if (!eligibility.eligible) {
      return { success: false, message: eligibility.reason };
    }

    // 更新奖励状态为已兑换
    const updatedReward = this.updateReward(rewardId, { 
      status: 'redeemed',
      redeemedAt: Date.now()
    });

    if (updatedReward) {
      return { 
        success: true, 
        message: '奖励兑换成功！',
        reward: updatedReward
      };
    }

    return { success: false, message: '兑换失败，请重试' };
  }

  /**
   * 获取奖励统计
   */
  getRewardStats() {
    const rewards = this.getAllRewards();
    
    return {
      total: rewards.length,
      active: rewards.filter(r => r.status === 'active').length,
      redeemed: rewards.filter(r => r.status === 'redeemed').length,
      pending: rewards.filter(r => {
        if (r.status !== 'active') return false;
        // 这里需要实际的孩子数据来检查
        // 暂时返回模拟数据
        return Math.random() > 0.7; // 30%的概率有待兑换奖励
      }).length
    };
  }
}

// 导出单例
const rewardSystem = new RewardSystem();

module.exports = {
  rewardSystem
};
