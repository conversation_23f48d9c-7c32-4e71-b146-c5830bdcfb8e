/* 《能量星球》宇宙灯塔 - 星系沉浸式主题 */

/* 宇宙容器 */
.lighthouse-universe {
  min-height: 100vh;
  background: radial-gradient(ellipse at center, #0F0C29 0%, #24243e 50%, #2E1065 100%);
  position: relative;
  overflow: hidden;
  padding: 0;
  box-sizing: border-box;
}

/* 宇宙背景层 */
.universe-background {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  z-index: 0;
}

/* 星空层 */
.starfield {
  position: absolute;
  width: 100%;
  height: 100%;
}

.star {
  position: absolute;
  width: 4rpx;
  height: 4rpx;
  background: #FFFFFF;
  border-radius: 50%;
  animation: starTwinkle 3s infinite ease-in-out;
}

.star-1 { top: 10%; left: 15%; animation-delay: 0s; }
.star-2 { top: 20%; right: 20%; animation-delay: 0.5s; }
.star-3 { top: 30%; left: 60%; animation-delay: 1s; }
.star-4 { top: 45%; right: 30%; animation-delay: 1.5s; }
.star-5 { top: 60%; left: 25%; animation-delay: 2s; }
.star-6 { top: 70%; right: 15%; animation-delay: 2.5s; }
.star-7 { top: 80%; left: 70%; animation-delay: 3s; }
.star-8 { top: 35%; left: 10%; animation-delay: 3.5s; }
.star-9 { top: 55%; right: 60%; animation-delay: 4s; }
.star-10 { top: 25%; left: 80%; animation-delay: 4.5s; }

@keyframes starTwinkle {
  0%, 100% {
    opacity: 0.3;
    transform: scale(1);
  }
  50% {
    opacity: 1;
    transform: scale(1.5);
  }
}

/* 星云层 */
.nebula {
  position: absolute;
  border-radius: 50%;
  background: radial-gradient(circle, rgba(138, 43, 226, 0.3) 0%, rgba(75, 0, 130, 0.2) 50%, transparent 70%);
  animation: nebulaFloat 15s infinite ease-in-out;
}

.nebula-1 {
  width: 600rpx;
  height: 400rpx;
  top: 10%;
  left: -200rpx;
  animation-delay: 0s;
}

.nebula-2 {
  width: 400rpx;
  height: 300rpx;
  top: 50%;
  right: -150rpx;
  animation-delay: 5s;
  background: radial-gradient(circle, rgba(255, 20, 147, 0.2) 0%, rgba(138, 43, 226, 0.1) 50%, transparent 70%);
}

.nebula-3 {
  width: 500rpx;
  height: 350rpx;
  bottom: 20%;
  left: 30%;
  animation-delay: 10s;
  background: radial-gradient(circle, rgba(72, 61, 139, 0.3) 0%, rgba(106, 90, 205, 0.2) 50%, transparent 70%);
}

@keyframes nebulaFloat {
  0%, 100% {
    transform: translateY(0) rotate(0deg) scale(1);
    opacity: 0.6;
  }
  33% {
    transform: translateY(-50rpx) rotate(120deg) scale(1.1);
    opacity: 0.8;
  }
  66% {
    transform: translateY(30rpx) rotate(240deg) scale(0.9);
    opacity: 0.7;
  }
}

/* 流星层 */
.shooting-stars {
  position: absolute;
  width: 100%;
  height: 100%;
}

.shooting-star {
  position: absolute;
  width: 2rpx;
  height: 2rpx;
  background: #FFFFFF;
  border-radius: 50%;
  animation: shootingStar 8s infinite linear;
}

.star-trail-1 {
  top: 20%;
  left: -20rpx;
  animation-delay: 0s;
}

.star-trail-2 {
  top: 60%;
  left: -20rpx;
  animation-delay: 3s;
}

.star-trail-3 {
  top: 40%;
  left: -20rpx;
  animation-delay: 6s;
}

@keyframes shootingStar {
  0% {
    transform: translateX(0) translateY(0);
    opacity: 0;
    box-shadow: 0 0 0 0 #FFFFFF;
  }
  10% {
    opacity: 1;
    box-shadow: -20rpx 0 20rpx 0 rgba(255, 255, 255, 0.5);
  }
  90% {
    opacity: 0.5;
    box-shadow: -40rpx 0 40rpx 0 rgba(255, 255, 255, 0.3);
  }
  100% {
    transform: translateX(calc(100vw + 100rpx)) translateY(-100rpx);
    opacity: 0;
    box-shadow: 0 0 0 0 #FFFFFF;
  }
}

/* 中央灯塔核心 */
.lighthouse-core {
  position: relative;
  z-index: 10;
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 60rpx 40rpx 40rpx;
}

.lighthouse-beacon {
  position: relative;
  width: 200rpx;
  height: 200rpx;
  margin-bottom: 40rpx;
}

.beacon-light {
  position: absolute;
  top: 50%;
  left: 50%;
  width: 100%;
  height: 100%;
  transform: translate(-50%, -50%);
  background: radial-gradient(circle, rgba(255, 215, 0, 0.8) 0%, rgba(255, 140, 0, 0.4) 50%, transparent 70%);
  border-radius: 50%;
  animation: beaconPulse 3s infinite ease-in-out;
}

.beacon-icon {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  font-size: 80rpx;
  z-index: 2;
  animation: beaconRotate 8s infinite linear;
  filter: drop-shadow(0 0 20rpx rgba(255, 215, 0, 0.8));
}

.beacon-rings {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 100%;
  height: 100%;
}

.ring {
  position: absolute;
  border: 2rpx solid rgba(255, 215, 0, 0.6);
  border-radius: 50%;
  animation: ringExpand 4s infinite ease-out;
}

.ring-1 {
  width: 120%;
  height: 120%;
  top: -10%;
  left: -10%;
  animation-delay: 0s;
}

.ring-2 {
  width: 140%;
  height: 140%;
  top: -20%;
  left: -20%;
  animation-delay: 1.3s;
}

.ring-3 {
  width: 160%;
  height: 160%;
  top: -30%;
  left: -30%;
  animation-delay: 2.6s;
}

@keyframes beaconPulse {
  0%, 100% {
    transform: translate(-50%, -50%) scale(1);
    opacity: 0.8;
  }
  50% {
    transform: translate(-50%, -50%) scale(1.2);
    opacity: 1;
  }
}

@keyframes beaconRotate {
  0% {
    transform: translate(-50%, -50%) rotate(0deg);
  }
  100% {
    transform: translate(-50%, -50%) rotate(360deg);
  }
}

@keyframes ringExpand {
  0% {
    transform: scale(0.8);
    opacity: 0.8;
  }
  100% {
    transform: scale(1.2);
    opacity: 0;
  }
}

.lighthouse-title {
  text-align: center;
  margin-bottom: 30rpx;
}

.title-main {
  font-size: 48rpx;
  font-weight: bold;
  color: #FFD700;
  margin-bottom: 10rpx;
  text-shadow: 0 0 20rpx rgba(255, 215, 0, 0.8);
  animation: titleGlow 4s infinite ease-in-out;
}

.title-sub {
  font-size: 28rpx;
  color: #E6E6FA;
  opacity: 0.9;
  text-shadow: 0 0 10rpx rgba(230, 230, 250, 0.5);
}

@keyframes titleGlow {
  0%, 100% {
    text-shadow: 0 0 20rpx rgba(255, 215, 0, 0.8);
  }
  50% {
    text-shadow: 0 0 30rpx rgba(255, 215, 0, 1), 0 0 40rpx rgba(255, 140, 0, 0.6);
  }
}

.season-badge {
  position: relative;
  padding: 12rpx 30rpx;
  border-radius: 25rpx;
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10rpx);
  border: 1rpx solid rgba(255, 215, 0, 0.3);
}

.badge-glow {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(45deg, rgba(255, 215, 0, 0.2), rgba(255, 140, 0, 0.1));
  border-radius: 25rpx;
  animation: badgeGlow 3s infinite ease-in-out;
}

.badge-text {
  position: relative;
  z-index: 1;
  font-size: 24rpx;
  color: #FFD700;
  font-weight: bold;
  text-align: center;
}

@keyframes badgeGlow {
  0%, 100% {
    opacity: 0.6;
  }
  50% {
    opacity: 1;
  }
}

/* 能量光环系统 */
.energy-aura-system {
  position: relative;
  z-index: 10;
  display: flex;
  justify-content: space-around;
  align-items: center;
  padding: 40rpx 20rpx;
  margin-bottom: 60rpx;
}

.love-energy-aura {
  position: relative;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.aura-core {
  position: relative;
  width: 120rpx;
  height: 120rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background: radial-gradient(circle, rgba(255, 20, 147, 0.3) 0%, rgba(255, 20, 147, 0.1) 70%, transparent 100%);
  border-radius: 50%;
  margin-bottom: 20rpx;
}

.energy-value {
  font-size: 32rpx;
  font-weight: bold;
  color: #FF1493;
  text-shadow: 0 0 15rpx rgba(255, 20, 147, 0.8);
  margin-bottom: 5rpx;
}

.energy-icon {
  font-size: 40rpx;
  animation: energyHeartbeat 2s infinite ease-in-out;
}

@keyframes energyHeartbeat {
  0%, 100% {
    transform: scale(1);
    filter: drop-shadow(0 0 10rpx rgba(255, 20, 147, 0.6));
  }
  50% {
    transform: scale(1.2);
    filter: drop-shadow(0 0 20rpx rgba(255, 20, 147, 1));
  }
}

.aura-ring {
  position: absolute;
  border: 2rpx solid rgba(255, 20, 147, 0.4);
  border-radius: 50%;
  animation: auraExpand 3s infinite ease-out;
}

.aura-ring-1 {
  width: 140rpx;
  height: 140rpx;
  top: -10rpx;
  left: -10rpx;
  animation-delay: 0s;
}

.aura-ring-2 {
  width: 160rpx;
  height: 160rpx;
  top: -20rpx;
  left: -20rpx;
  animation-delay: 1s;
}

.aura-ring-3 {
  width: 180rpx;
  height: 180rpx;
  top: -30rpx;
  left: -30rpx;
  animation-delay: 2s;
}

@keyframes auraExpand {
  0% {
    transform: scale(0.8);
    opacity: 0.6;
  }
  100% {
    transform: scale(1.2);
    opacity: 0;
  }
}

.energy-label {
  font-size: 22rpx;
  color: #E6E6FA;
  text-align: center;
  opacity: 0.8;
}

.level-aura {
  position: relative;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.level-core {
  position: relative;
  width: 120rpx;
  height: 120rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background: radial-gradient(circle, rgba(138, 43, 226, 0.3) 0%, rgba(138, 43, 226, 0.1) 70%, transparent 100%);
  border-radius: 50%;
  margin-bottom: 20rpx;
}

.level-icon {
  font-size: 50rpx;
  margin-bottom: 5rpx;
  animation: levelFloat 4s infinite ease-in-out;
}

@keyframes levelFloat {
  0%, 100% {
    transform: translateY(0) rotate(0deg);
  }
  50% {
    transform: translateY(-10rpx) rotate(180deg);
  }
}

.level-name {
  font-size: 20rpx;
  color: #DDA0DD;
  text-align: center;
  font-weight: bold;
}

.level-progress-ring {
  position: relative;
  width: 140rpx;
  height: 140rpx;
  margin-top: 10rpx;
}

.progress-arc {
  position: absolute;
  width: 100%;
  height: 100%;
  border-radius: 50%;
  background: conic-gradient(from 0deg, #8A2BE2 0%, #8A2BE2 var(--progress, 0%), transparent var(--progress, 0%));
  mask: radial-gradient(circle, transparent 50rpx, black 52rpx);
  animation: arcRotate 8s infinite linear;
}

@keyframes arcRotate {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

.progress-text {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  font-size: 18rpx;
  color: #DDA0DD;
  text-align: center;
  white-space: nowrap;
}

.lighthouse-icon {
  font-size: 80rpx;
  margin-bottom: 20rpx;
  animation: lighthouseBeacon 4s infinite ease-in-out;
}

@keyframes lighthouseBeacon {
  0%, 100% {
    transform: scale(1) rotate(0deg);
    filter: drop-shadow(0 0 20rpx rgba(255, 105, 180, 0.8)) drop-shadow(0 0 40rpx rgba(156, 39, 176, 0.4));
  }
  25% {
    transform: scale(1.05) rotate(90deg);
    filter: drop-shadow(0 0 30rpx rgba(233, 30, 99, 0.9)) drop-shadow(0 0 50rpx rgba(106, 27, 154, 0.5));
  }
  50% {
    transform: scale(1.1) rotate(180deg);
    filter: drop-shadow(0 0 40rpx rgba(255, 64, 129, 1)) drop-shadow(0 0 60rpx rgba(74, 20, 140, 0.6));
  }
  75% {
    transform: scale(1.05) rotate(270deg);
    filter: drop-shadow(0 0 30rpx rgba(233, 30, 99, 0.9)) drop-shadow(0 0 50rpx rgba(106, 27, 154, 0.5));
  }
}

.header-title {
  font-size: 48rpx;
  font-weight: bold;
  color: #FF69B4;
  margin-bottom: 10rpx;
  text-shadow: 2rpx 2rpx 8rpx rgba(255, 105, 180, 0.6);
  background: linear-gradient(45deg, #FF69B4, #E91E63, #9C27B0);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.header-subtitle {
  font-size: 28rpx;
  color: #E1BEE7;
  margin-bottom: 20rpx;
  text-shadow: 1rpx 1rpx 4rpx rgba(156, 39, 176, 0.4);
}

/* 季节主题 */
.seasonal-theme {
  margin-top: 20rpx;
}

.theme-badge {
  display: inline-block;
  padding: 8rpx 20rpx;
  border-radius: 20rpx;
  color: white;
  font-size: 24rpx;
  font-weight: bold;
  margin-bottom: 10rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.2);
}

.theme-description {
  font-size: 24rpx;
  color: #BF360C;
  opacity: 0.8;
}

/* 用户爱心状态卡片 */
.love-status-card {
  position: relative;
  z-index: 10;
  display: flex;
  justify-content: space-between;
  align-items: center;
  background: rgba(255, 255, 255, 0.12);
  border-radius: 25rpx;
  padding: 30rpx;
  margin-bottom: 40rpx;
  backdrop-filter: blur(20rpx);
  border: 2rpx solid rgba(255, 105, 180, 0.5);
  box-shadow: 0 8rpx 32rpx rgba(255, 105, 180, 0.25);
  animation: statusCardGlow 8s infinite ease-in-out;
}

@keyframes statusCardGlow {
  0%, 100% {
    box-shadow: 0 8rpx 32rpx rgba(255, 105, 180, 0.25);
    border-color: rgba(255, 105, 180, 0.5);
  }
  50% {
    box-shadow: 0 12rpx 48rpx rgba(233, 30, 99, 0.4);
    border-color: rgba(233, 30, 99, 0.7);
  }
}

.love-energy-display {
  text-align: center;
}

.energy-icon {
  font-size: 60rpx;
  margin-bottom: 10rpx;
  animation: energyPulse 2s infinite ease-in-out;
}

@keyframes energyPulse {
  0%, 100% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.1);
  }
}

.energy-amount {
  font-size: 36rpx;
  font-weight: bold;
  color: #E1BEE7;
  margin-bottom: 5rpx;
  text-shadow: 1rpx 1rpx 4rpx rgba(156, 39, 176, 0.6);
}

.energy-label {
  font-size: 22rpx;
  color: #CE93D8;
  text-shadow: 1rpx 1rpx 2rpx rgba(106, 27, 154, 0.4);
}

.love-level-display {
  display: flex;
  align-items: center;
  flex: 1;
  margin-left: 30rpx;
}

.level-icon {
  font-size: 50rpx;
  margin-right: 20rpx;
}

.level-info {
  flex: 1;
}

.level-name {
  font-size: 28rpx;
  font-weight: bold;
  color: #E1BEE7;
  margin-bottom: 10rpx;
  text-shadow: 1rpx 1rpx 4rpx rgba(156, 39, 176, 0.6);
}

.level-progress {
  display: flex;
  align-items: center;
}

.progress-bar {
  flex: 1;
  height: 12rpx;
  background: rgba(255, 105, 180, 0.3);
  border-radius: 6rpx;
  overflow: hidden;
  margin-right: 15rpx;
}

.progress-fill {
  height: 100%;
  background: linear-gradient(90deg, #FF69B4, #E91E63, #9C27B0);
  border-radius: 6rpx;
  transition: width 0.5s ease;
  box-shadow: 0 0 10rpx rgba(255, 105, 180, 0.6);
}

.progress-text {
  font-size: 20rpx;
  color: #CE93D8;
  white-space: nowrap;
  text-shadow: 1rpx 1rpx 2rpx rgba(106, 27, 154, 0.4);
}

.level-max {
  font-size: 24rpx;
  color: #FFD54F;
  font-weight: bold;
}

/* 六大功能模块网格 */
.modules-grid {
  position: relative;
  z-index: 10;
  margin-bottom: 40rpx;
}

.module-row {
  display: flex;
  justify-content: space-between;
  margin-bottom: 30rpx;
}

.charity-module {
  position: relative;
  width: 48%;
  aspect-ratio: 1;
  background: rgba(255, 255, 255, 0.15);
  border-radius: 25rpx;
  padding: 30rpx 20rpx;
  text-align: center;
  backdrop-filter: blur(20rpx);
  border: 2rpx solid rgba(255, 105, 180, 0.4);
  box-shadow: 0 8rpx 32rpx rgba(255, 105, 180, 0.2);
  transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  overflow: hidden;
  animation: moduleFloating 6s infinite ease-in-out;
}

.charity-module:nth-child(1) { animation-delay: 0s; }
.charity-module:nth-child(2) { animation-delay: 1s; }

.charity-module:active {
  transform: scale(0.92) translateY(-10rpx);
  box-shadow: 0 12rpx 48rpx rgba(255, 105, 180, 0.4);
  border-color: rgba(233, 30, 99, 0.6);
}

@keyframes moduleFloating {
  0%, 100% {
    transform: translateY(0) rotate(0deg);
  }
  25% {
    transform: translateY(-8rpx) rotate(1deg);
  }
  50% {
    transform: translateY(-12rpx) rotate(0deg);
  }
  75% {
    transform: translateY(-8rpx) rotate(-1deg);
  }
}

.module-icon {
  font-size: 60rpx;
  margin-bottom: 15rpx;
  animation: moduleIconDance 5s infinite ease-in-out;
  filter: drop-shadow(0 0 15rpx rgba(255, 105, 180, 0.6));
}

@keyframes moduleIconDance {
  0%, 100% {
    transform: translateY(0) scale(1) rotate(0deg);
    filter: drop-shadow(0 0 15rpx rgba(255, 105, 180, 0.6));
  }
  20% {
    transform: translateY(-5rpx) scale(1.05) rotate(5deg);
    filter: drop-shadow(0 0 20rpx rgba(233, 30, 99, 0.8));
  }
  40% {
    transform: translateY(-8rpx) scale(1.1) rotate(-3deg);
    filter: drop-shadow(0 0 25rpx rgba(255, 64, 129, 1));
  }
  60% {
    transform: translateY(-5rpx) scale(1.05) rotate(3deg);
    filter: drop-shadow(0 0 20rpx rgba(233, 30, 99, 0.8));
  }
  80% {
    transform: translateY(-2rpx) scale(1.02) rotate(-1deg);
    filter: drop-shadow(0 0 18rpx rgba(255, 105, 180, 0.7));
  }
}

.module-title {
  font-size: 26rpx;
  font-weight: bold;
  color: #E1BEE7;
  margin-bottom: 8rpx;
  line-height: 1.3;
  text-shadow: 1rpx 1rpx 4rpx rgba(156, 39, 176, 0.4);
}

.module-subtitle {
  font-size: 20rpx;
  color: #CE93D8;
  opacity: 0.9;
  text-shadow: 1rpx 1rpx 2rpx rgba(106, 27, 154, 0.3);
}

/* 模块发光效果 */
.module-glow {
  position: absolute;
  top: 50%;
  left: 50%;
  width: 80%;
  height: 80%;
  transform: translate(-50%, -50%);
  border-radius: 50%;
  opacity: 0;
  transition: opacity 0.3s ease;
  pointer-events: none;
}

.charity-module:active .module-glow {
  opacity: 0.6;
  animation: moduleGlow 0.6s ease-out;
}

@keyframes moduleGlow {
  0% {
    transform: translate(-50%, -50%) scale(0.5);
    opacity: 0.8;
  }
  100% {
    transform: translate(-50%, -50%) scale(1.2);
    opacity: 0;
  }
}

.project-glow { background: radial-gradient(circle, rgba(255, 193, 7, 0.6) 0%, transparent 70%); }
.donation-glow { background: radial-gradient(circle, rgba(233, 30, 99, 0.6) 0%, transparent 70%); }
.kindness-glow { background: radial-gradient(circle, rgba(76, 175, 80, 0.6) 0%, transparent 70%); }
.growth-glow { background: radial-gradient(circle, rgba(63, 81, 181, 0.6) 0%, transparent 70%); }
.story-glow { background: radial-gradient(circle, rgba(156, 39, 176, 0.6) 0%, transparent 70%); }
.family-glow { background: radial-gradient(circle, rgba(255, 87, 34, 0.6) 0%, transparent 70%); }

/* 快速捐赠区域 */
.quick-donation-section {
  position: relative;
  z-index: 10;
  margin-bottom: 40rpx;
}

.section-title {
  display: flex;
  align-items: center;
  margin-bottom: 20rpx;
  padding: 0 10rpx;
}

.title-icon {
  font-size: 32rpx;
  margin-right: 15rpx;
}

.title-text {
  font-size: 32rpx;
  font-weight: bold;
  color: #E65100;
}

.recommended-project-card {
  background: rgba(255, 255, 255, 0.3);
  border-radius: 20rpx;
  padding: 25rpx;
  backdrop-filter: blur(15rpx);
  border: 2rpx solid rgba(255, 183, 77, 0.4);
  box-shadow: 0 6rpx 24rpx rgba(255, 183, 77, 0.2);
  transition: all 0.3s ease;
}

.recommended-project-card:active {
  transform: scale(0.98);
  box-shadow: 0 3rpx 12rpx rgba(255, 183, 77, 0.3);
}

.project-info {
  display: flex;
  align-items: center;
  margin-bottom: 20rpx;
}

.project-icon {
  font-size: 50rpx;
  margin-right: 20rpx;
}

.project-details {
  flex: 1;
}

.project-title {
  font-size: 28rpx;
  font-weight: bold;
  color: #E65100;
  margin-bottom: 10rpx;
}

.project-progress {
  display: flex;
  align-items: center;
}

.quick-donate-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #FF8A65, #FFB74D);
  border-radius: 15rpx;
  padding: 15rpx 25rpx;
  color: white;
  font-weight: bold;
  box-shadow: 0 4rpx 16rpx rgba(255, 138, 101, 0.4);
}

.btn-text {
  font-size: 26rpx;
  margin-right: 10rpx;
}

.btn-cost {
  font-size: 24rpx;
  opacity: 0.9;
}

/* 最近活动 */
.recent-activities {
  position: relative;
  z-index: 10;
  margin-bottom: 40rpx;
}

.activity-list {
  background: rgba(255, 255, 255, 0.25);
  border-radius: 20rpx;
  padding: 20rpx;
  backdrop-filter: blur(15rpx);
  border: 2rpx solid rgba(255, 183, 77, 0.3);
}

.activity-item {
  display: flex;
  align-items: center;
  padding: 20rpx 0;
  border-bottom: 1rpx solid rgba(255, 183, 77, 0.2);
}

.activity-item:last-child {
  border-bottom: none;
}

.activity-icon {
  font-size: 40rpx;
  margin-right: 20rpx;
}

.activity-info {
  flex: 1;
}

.activity-title {
  font-size: 26rpx;
  font-weight: bold;
  color: #E65100;
  margin-bottom: 5rpx;
}

.activity-detail {
  font-size: 22rpx;
  color: #FF8A65;
  margin-bottom: 3rpx;
}

.activity-time {
  font-size: 20rpx;
  color: #FFAB91;
  opacity: 0.8;
}

.activity-badge {
  font-size: 30rpx;
  animation: badgePulse 2s infinite ease-in-out;
}

@keyframes badgePulse {
  0%, 100% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.1);
  }
}

/* 爱心传递链 */
.love-chain-section {
  position: relative;
  z-index: 10;
  margin-bottom: 40rpx;
}

.love-chain-visual {
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba(255, 255, 255, 0.25);
  border-radius: 20rpx;
  padding: 30rpx 20rpx;
  backdrop-filter: blur(15rpx);
  border: 2rpx solid rgba(255, 183, 77, 0.3);
  margin-bottom: 15rpx;
}

.chain-node {
  text-align: center;
  flex: 1;
}

.node-avatar {
  font-size: 50rpx;
  margin-bottom: 10rpx;
  animation: nodeFloat 3s infinite ease-in-out;
}

@keyframes nodeFloat {
  0%, 100% {
    transform: translateY(0);
  }
  50% {
    transform: translateY(-5rpx);
  }
}

.node-name {
  font-size: 22rpx;
  color: #E65100;
  font-weight: bold;
}

.chain-link {
  font-size: 30rpx;
  margin: 0 10rpx;
  animation: linkPulse 2s infinite ease-in-out;
}

@keyframes linkPulse {
  0%, 100% {
    transform: scale(1);
    opacity: 0.8;
  }
  50% {
    transform: scale(1.2);
    opacity: 1;
  }
}

.chain-description {
  text-align: center;
  font-size: 24rpx;
  color: #FF8A65;
  padding: 0 20rpx;
}

/* 加载状态 */
.loading-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(255, 243, 224, 0.8);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  backdrop-filter: blur(5rpx);
}

.loading-spinner {
  text-align: center;
}

.spinner-heart {
  font-size: 80rpx;
  animation: spinnerRotate 1.5s infinite ease-in-out;
}

@keyframes spinnerRotate {
  0% {
    transform: rotate(0deg) scale(1);
  }
  50% {
    transform: rotate(180deg) scale(1.2);
  }
  100% {
    transform: rotate(360deg) scale(1);
  }
}

.loading-text {
  font-size: 28rpx;
  color: #E65100;
  margin-top: 20rpx;
  font-weight: bold;
}

/* 庆祝动画 */
.celebration-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(255, 243, 224, 0.9);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1001;
  backdrop-filter: blur(10rpx);
}

.celebration-content {
  text-align: center;
  position: relative;
}

.celebration-icon {
  font-size: 120rpx;
  margin-bottom: 30rpx;
  animation: celebrationBounce 0.8s ease-out;
}

@keyframes celebrationBounce {
  0% {
    transform: scale(0) rotate(0deg);
  }
  50% {
    transform: scale(1.3) rotate(180deg);
  }
  100% {
    transform: scale(1) rotate(360deg);
  }
}

.celebration-message {
  font-size: 36rpx;
  font-weight: bold;
  color: #E65100;
  margin-bottom: 40rpx;
  text-shadow: 2rpx 2rpx 4rpx rgba(0, 0, 0, 0.1);
}

.celebration-hearts {
  position: absolute;
  top: 0;
  left: 50%;
  transform: translateX(-50%);
  width: 300rpx;
  height: 300rpx;
}

.celebration-heart {
  position: absolute;
  font-size: 40rpx;
  animation: celebrationHeartFloat 2s ease-out forwards;
}

.celebration-heart.heart-1 {
  top: 50%;
  left: 50%;
  animation-delay: 0.2s;
}

.celebration-heart.heart-2 {
  top: 30%;
  left: 30%;
  animation-delay: 0.4s;
}

.celebration-heart.heart-3 {
  top: 30%;
  right: 30%;
  animation-delay: 0.6s;
}

.celebration-heart.heart-4 {
  bottom: 30%;
  left: 30%;
  animation-delay: 0.8s;
}

.celebration-heart.heart-5 {
  bottom: 30%;
  right: 30%;
  animation-delay: 1s;
}

@keyframes celebrationHeartFloat {
  0% {
    transform: translate(-50%, -50%) scale(0);
    opacity: 1;
  }
  50% {
    transform: translate(-50%, -50%) scale(1.2);
    opacity: 0.8;
  }
  100% {
    transform: translate(-50%, -150rpx) scale(0.8);
    opacity: 0;
  }
}
