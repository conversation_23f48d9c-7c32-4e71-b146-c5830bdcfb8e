/* 《能量星球》宇宙灯塔 - 星系沉浸式主题 */

/* 宇宙容器 */
.lighthouse-universe {
  min-height: 100vh;
  background: radial-gradient(ellipse at center, #0F0C29 0%, #24243e 50%, #2E1065 100%);
  position: relative;
  overflow: hidden;
  padding: 0;
  box-sizing: border-box;
}

/* 宇宙背景层 */
.universe-background {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  z-index: 0;
}

/* 星空层 */
.starfield {
  position: absolute;
  width: 100%;
  height: 100%;
}

.star {
  position: absolute;
  width: 4rpx;
  height: 4rpx;
  background: #FFFFFF;
  border-radius: 50%;
  animation: starTwinkle 3s infinite ease-in-out;
}

.star-1 { top: 10%; left: 15%; animation-delay: 0s; }
.star-2 { top: 20%; right: 20%; animation-delay: 0.5s; }
.star-3 { top: 30%; left: 60%; animation-delay: 1s; }
.star-4 { top: 45%; right: 30%; animation-delay: 1.5s; }
.star-5 { top: 60%; left: 25%; animation-delay: 2s; }
.star-6 { top: 70%; right: 15%; animation-delay: 2.5s; }
.star-7 { top: 80%; left: 70%; animation-delay: 3s; }
.star-8 { top: 35%; left: 10%; animation-delay: 3.5s; }
.star-9 { top: 55%; right: 60%; animation-delay: 4s; }
.star-10 { top: 25%; left: 80%; animation-delay: 4.5s; }

@keyframes starTwinkle {
  0%, 100% {
    opacity: 0.3;
    transform: scale(1);
  }
  50% {
    opacity: 1;
    transform: scale(1.5);
  }
}

/* 星云层 */
.nebula {
  position: absolute;
  border-radius: 50%;
  background: radial-gradient(circle, rgba(138, 43, 226, 0.3) 0%, rgba(75, 0, 130, 0.2) 50%, transparent 70%);
  animation: nebulaFloat 15s infinite ease-in-out;
}

.nebula-1 {
  width: 600rpx;
  height: 400rpx;
  top: 10%;
  left: -200rpx;
  animation-delay: 0s;
}

.nebula-2 {
  width: 400rpx;
  height: 300rpx;
  top: 50%;
  right: -150rpx;
  animation-delay: 5s;
  background: radial-gradient(circle, rgba(255, 20, 147, 0.2) 0%, rgba(138, 43, 226, 0.1) 50%, transparent 70%);
}

.nebula-3 {
  width: 500rpx;
  height: 350rpx;
  bottom: 20%;
  left: 30%;
  animation-delay: 10s;
  background: radial-gradient(circle, rgba(72, 61, 139, 0.3) 0%, rgba(106, 90, 205, 0.2) 50%, transparent 70%);
}

@keyframes nebulaFloat {
  0%, 100% {
    transform: translateY(0) rotate(0deg) scale(1);
    opacity: 0.6;
  }
  33% {
    transform: translateY(-50rpx) rotate(120deg) scale(1.1);
    opacity: 0.8;
  }
  66% {
    transform: translateY(30rpx) rotate(240deg) scale(0.9);
    opacity: 0.7;
  }
}

/* 流星层 */
.shooting-stars {
  position: absolute;
  width: 100%;
  height: 100%;
}

.shooting-star {
  position: absolute;
  width: 2rpx;
  height: 2rpx;
  background: #FFFFFF;
  border-radius: 50%;
  animation: shootingStar 8s infinite linear;
}

.star-trail-1 {
  top: 20%;
  left: -20rpx;
  animation-delay: 0s;
}

.star-trail-2 {
  top: 60%;
  left: -20rpx;
  animation-delay: 3s;
}

.star-trail-3 {
  top: 40%;
  left: -20rpx;
  animation-delay: 6s;
}

@keyframes shootingStar {
  0% {
    transform: translateX(0) translateY(0);
    opacity: 0;
    box-shadow: 0 0 0 0 #FFFFFF;
  }
  10% {
    opacity: 1;
    box-shadow: -20rpx 0 20rpx 0 rgba(255, 255, 255, 0.5);
  }
  90% {
    opacity: 0.5;
    box-shadow: -40rpx 0 40rpx 0 rgba(255, 255, 255, 0.3);
  }
  100% {
    transform: translateX(calc(100vw + 100rpx)) translateY(-100rpx);
    opacity: 0;
    box-shadow: 0 0 0 0 #FFFFFF;
  }
}

/* 灯塔头部区域 */
.lighthouse-header-section {
  position: relative;
  z-index: 10;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 40rpx 20rpx;
  margin-bottom: 40rpx;
}

/* 中央灯塔核心 */
.lighthouse-core {
  position: relative;
  display: flex;
  flex-direction: column;
  align-items: center;
  flex: 1;
  max-width: 400rpx;
}

.lighthouse-beacon {
  position: relative;
  width: 160rpx;
  height: 160rpx;
  margin-bottom: 30rpx;
}

.beacon-light {
  position: absolute;
  top: 50%;
  left: 50%;
  width: 100%;
  height: 100%;
  transform: translate(-50%, -50%);
  background: radial-gradient(circle, rgba(255, 215, 0, 0.8) 0%, rgba(255, 140, 0, 0.4) 50%, transparent 70%);
  border-radius: 50%;
  animation: beaconPulse 3s infinite ease-in-out;
}

.beacon-icon {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  font-size: 60rpx;
  z-index: 2;
  animation: beaconRotate 8s infinite linear;
  filter: drop-shadow(0 0 20rpx rgba(255, 215, 0, 0.8));
}

.beacon-rings {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 100%;
  height: 100%;
}

.ring {
  position: absolute;
  border: 2rpx solid rgba(255, 215, 0, 0.6);
  border-radius: 50%;
  animation: ringExpand 4s infinite ease-out;
}

.ring-1 {
  width: 120%;
  height: 120%;
  top: -10%;
  left: -10%;
  animation-delay: 0s;
}

.ring-2 {
  width: 140%;
  height: 140%;
  top: -20%;
  left: -20%;
  animation-delay: 1.3s;
}

.ring-3 {
  width: 160%;
  height: 160%;
  top: -30%;
  left: -30%;
  animation-delay: 2.6s;
}

@keyframes beaconPulse {
  0%, 100% {
    transform: translate(-50%, -50%) scale(1);
    opacity: 0.8;
  }
  50% {
    transform: translate(-50%, -50%) scale(1.2);
    opacity: 1;
  }
}

@keyframes beaconRotate {
  0% {
    transform: translate(-50%, -50%) rotate(0deg);
  }
  100% {
    transform: translate(-50%, -50%) rotate(360deg);
  }
}

@keyframes ringExpand {
  0% {
    transform: scale(0.8);
    opacity: 0.8;
  }
  100% {
    transform: scale(1.2);
    opacity: 0;
  }
}

.lighthouse-title {
  text-align: center;
  margin-bottom: 30rpx;
}

.title-main {
  font-size: 36rpx;
  font-weight: bold;
  color: #FFD700;
  margin-bottom: 8rpx;
  text-shadow: 0 0 20rpx rgba(255, 215, 0, 0.8);
  animation: titleGlow 4s infinite ease-in-out;
}

.title-sub {
  font-size: 22rpx;
  color: #E6E6FA;
  opacity: 0.9;
  text-shadow: 0 0 10rpx rgba(230, 230, 250, 0.5);
}

@keyframes titleGlow {
  0%, 100% {
    text-shadow: 0 0 20rpx rgba(255, 215, 0, 0.8);
  }
  50% {
    text-shadow: 0 0 30rpx rgba(255, 215, 0, 1), 0 0 40rpx rgba(255, 140, 0, 0.6);
  }
}

.season-badge {
  position: relative;
  padding: 12rpx 30rpx;
  border-radius: 25rpx;
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10rpx);
  border: 1rpx solid rgba(255, 215, 0, 0.3);
}

.badge-glow {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(45deg, rgba(255, 215, 0, 0.2), rgba(255, 140, 0, 0.1));
  border-radius: 25rpx;
  animation: badgeGlow 3s infinite ease-in-out;
}

.badge-text {
  position: relative;
  z-index: 1;
  font-size: 24rpx;
  color: #FFD700;
  font-weight: bold;
  text-align: center;
}

@keyframes badgeGlow {
  0%, 100% {
    opacity: 0.6;
  }
  50% {
    opacity: 1;
  }
}

/* 左侧爱心能量光环 */
.left-energy-aura {
  position: relative;
  display: flex;
  flex-direction: column;
  align-items: center;
  width: 140rpx;
}

/* 右侧等级光环 */
.right-level-aura {
  position: relative;
  display: flex;
  flex-direction: column;
  align-items: center;
  width: 140rpx;
}

.aura-core {
  position: relative;
  width: 100rpx;
  height: 100rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background: radial-gradient(circle, rgba(255, 20, 147, 0.3) 0%, rgba(255, 20, 147, 0.1) 70%, transparent 100%);
  border-radius: 50%;
  margin-bottom: 15rpx;
}

.energy-value {
  font-size: 28rpx;
  font-weight: bold;
  color: #FF1493;
  text-shadow: 0 0 15rpx rgba(255, 20, 147, 0.8);
  margin-bottom: 3rpx;
}

.energy-icon {
  font-size: 32rpx;
  animation: energyHeartbeat 2s infinite ease-in-out;
}

@keyframes energyHeartbeat {
  0%, 100% {
    transform: scale(1);
    filter: drop-shadow(0 0 10rpx rgba(255, 20, 147, 0.6));
  }
  50% {
    transform: scale(1.2);
    filter: drop-shadow(0 0 20rpx rgba(255, 20, 147, 1));
  }
}

.aura-ring {
  position: absolute;
  border: 2rpx solid rgba(255, 20, 147, 0.4);
  border-radius: 50%;
  animation: auraExpand 3s infinite ease-out;
}

.aura-ring-1 {
  width: 120rpx;
  height: 120rpx;
  top: -10rpx;
  left: -10rpx;
  animation-delay: 0s;
}

.aura-ring-2 {
  width: 140rpx;
  height: 140rpx;
  top: -20rpx;
  left: -20rpx;
  animation-delay: 1s;
}

.aura-ring-3 {
  width: 160rpx;
  height: 160rpx;
  top: -30rpx;
  left: -30rpx;
  animation-delay: 2s;
}

@keyframes auraExpand {
  0% {
    transform: scale(0.8);
    opacity: 0.6;
  }
  100% {
    transform: scale(1.2);
    opacity: 0;
  }
}

.energy-label {
  font-size: 18rpx;
  color: #E6E6FA;
  text-align: center;
  opacity: 0.8;
}



.level-core {
  position: relative;
  width: 100rpx;
  height: 100rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background: radial-gradient(circle, rgba(138, 43, 226, 0.3) 0%, rgba(138, 43, 226, 0.1) 70%, transparent 100%);
  border-radius: 50%;
  margin-bottom: 15rpx;
}

.level-icon {
  font-size: 40rpx;
  margin-bottom: 3rpx;
  animation: levelFloat 4s infinite ease-in-out;
}

@keyframes levelFloat {
  0%, 100% {
    transform: translateY(0) rotate(0deg);
  }
  50% {
    transform: translateY(-8rpx) rotate(180deg);
  }
}

.level-name {
  font-size: 16rpx;
  color: #DDA0DD;
  text-align: center;
  font-weight: bold;
}

.level-progress-ring {
  position: relative;
  width: 120rpx;
  height: 120rpx;
  margin-top: 8rpx;
}

.progress-arc {
  position: absolute;
  width: 100%;
  height: 100%;
  border-radius: 50%;
  background: conic-gradient(from 0deg, #8A2BE2 0%, #8A2BE2 var(--progress, 0%), transparent var(--progress, 0%));
  mask: radial-gradient(circle, transparent 50rpx, black 52rpx);
  animation: arcRotate 8s infinite linear;
}

@keyframes arcRotate {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

.progress-text {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  font-size: 16rpx;
  color: #DDA0DD;
  text-align: center;
  white-space: nowrap;
}

/* 星系功能导航 */
.galaxy-navigation {
  position: relative;
  z-index: 10;
  padding: 40rpx 20rpx;
  display: flex;
  flex-direction: column;
  gap: 80rpx;
}

.function-planet {
  position: relative;
  display: flex;
  align-items: center;
  padding: 20rpx;
  transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

.function-planet:nth-child(odd) {
  flex-direction: row;
  justify-content: flex-start;
}

.function-planet:nth-child(even) {
  flex-direction: row-reverse;
  justify-content: flex-start;
}

.planet-core {
  position: relative;
  width: 120rpx;
  height: 120rpx;
  margin: 0 40rpx;
}

.planet-icon {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  font-size: 60rpx;
  z-index: 3;
  animation: planetFloat 6s infinite ease-in-out;
}

.planet-atmosphere {
  position: absolute;
  top: 50%;
  left: 50%;
  width: 100%;
  height: 100%;
  transform: translate(-50%, -50%);
  background: radial-gradient(circle, rgba(138, 43, 226, 0.4) 0%, rgba(75, 0, 130, 0.2) 50%, transparent 70%);
  border-radius: 50%;
  animation: atmosphereGlow 4s infinite ease-in-out;
}

.planet-ring {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  border: 1rpx solid rgba(255, 215, 0, 0.6);
  border-radius: 50%;
  animation: planetRingRotate 8s infinite linear;
}

.planet-ring.ring-1 {
  width: 140rpx;
  height: 140rpx;
  animation-delay: 0s;
}

.planet-ring.ring-2 {
  width: 160rpx;
  height: 160rpx;
  animation-delay: 2s;
  border-color: rgba(255, 20, 147, 0.4);
}

@keyframes planetFloat {
  0%, 100% {
    transform: translate(-50%, -50%) translateY(0) rotate(0deg);
    filter: drop-shadow(0 0 15rpx rgba(138, 43, 226, 0.6));
  }
  25% {
    transform: translate(-50%, -50%) translateY(-8rpx) rotate(90deg);
    filter: drop-shadow(0 0 20rpx rgba(255, 20, 147, 0.8));
  }
  50% {
    transform: translate(-50%, -50%) translateY(-12rpx) rotate(180deg);
    filter: drop-shadow(0 0 25rpx rgba(255, 215, 0, 1));
  }
  75% {
    transform: translate(-50%, -50%) translateY(-8rpx) rotate(270deg);
    filter: drop-shadow(0 0 20rpx rgba(255, 20, 147, 0.8));
  }
}

@keyframes atmosphereGlow {
  0%, 100% {
    opacity: 0.6;
    transform: translate(-50%, -50%) scale(1);
  }
  50% {
    opacity: 0.9;
    transform: translate(-50%, -50%) scale(1.1);
  }
}

@keyframes planetRingRotate {
  0% {
    transform: translate(-50%, -50%) rotate(0deg);
    opacity: 0.6;
  }
  50% {
    opacity: 1;
  }
  100% {
    transform: translate(-50%, -50%) rotate(360deg);
    opacity: 0.6;
  }
}

.planet-label {
  flex: 1;
  padding: 20rpx;
}

.function-planet:nth-child(even) .planet-label {
  text-align: right;
}

.label-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #E6E6FA;
  margin-bottom: 8rpx;
  text-shadow: 0 0 10rpx rgba(230, 230, 250, 0.6);
}

.label-subtitle {
  font-size: 24rpx;
  color: #DDA0DD;
  opacity: 0.8;
  text-shadow: 0 0 5rpx rgba(221, 160, 221, 0.4);
}

.planet-orbit-line {
  position: absolute;
  height: 2rpx;
  background: linear-gradient(90deg, transparent 0%, rgba(255, 215, 0, 0.6) 50%, transparent 100%);
  animation: orbitPulse 3s infinite ease-in-out;
  z-index: 1;
}

.function-planet:nth-child(odd) .planet-orbit-line {
  left: 180rpx;
  right: 20rpx;
  top: 50%;
}

.function-planet:nth-child(even) .planet-orbit-line {
  right: 180rpx;
  left: 20rpx;
  top: 50%;
}

.line-1 { animation-delay: 0s; }
.line-2 { animation-delay: 0.5s; }
.line-3 { animation-delay: 1s; }
.line-4 { animation-delay: 1.5s; }
.line-5 { animation-delay: 2s; }
.line-6 { animation-delay: 2.5s; }

@keyframes orbitPulse {
  0%, 100% {
    opacity: 0.3;
    transform: scaleY(1);
  }
  50% {
    opacity: 0.8;
    transform: scaleY(2);
  }
}

/* 行星特殊效果 */
.planet-projects .planet-atmosphere {
  background: radial-gradient(circle, rgba(255, 193, 7, 0.4) 0%, rgba(255, 152, 0, 0.2) 50%, transparent 70%);
}

.planet-donation .planet-atmosphere {
  background: radial-gradient(circle, rgba(255, 20, 147, 0.4) 0%, rgba(233, 30, 99, 0.2) 50%, transparent 70%);
}

.planet-kindness .planet-atmosphere {
  background: radial-gradient(circle, rgba(76, 175, 80, 0.4) 0%, rgba(139, 195, 74, 0.2) 50%, transparent 70%);
}

.planet-growth .planet-atmosphere {
  background: radial-gradient(circle, rgba(63, 81, 181, 0.4) 0%, rgba(103, 58, 183, 0.2) 50%, transparent 70%);
}

.planet-stories .planet-atmosphere {
  background: radial-gradient(circle, rgba(156, 39, 176, 0.4) 0%, rgba(142, 36, 170, 0.2) 50%, transparent 70%);
}

.planet-family .planet-atmosphere {
  background: radial-gradient(circle, rgba(255, 87, 34, 0.4) 0%, rgba(255, 111, 97, 0.2) 50%, transparent 70%);
}

/* 点击效果 */
.function-planet:active {
  transform: scale(0.95);
}

.function-planet:active .planet-core {
  animation: planetClickPulse 0.3s ease-out;
}

@keyframes planetClickPulse {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.2);
  }
  100% {
    transform: scale(1);
  }
}

/* 快速捐赠区域 */
.quick-donation-section {
  position: relative;
  z-index: 10;
  margin: 40rpx 20rpx;
  padding: 30rpx;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 25rpx;
  backdrop-filter: blur(15rpx);
  border: 1rpx solid rgba(255, 215, 0, 0.3);
}

.section-title {
  display: flex;
  align-items: center;
  margin-bottom: 20rpx;
}

.title-icon {
  font-size: 32rpx;
  margin-right: 15rpx;
  animation: titleIconSpin 4s infinite linear;
}

@keyframes titleIconSpin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

.title-text {
  font-size: 32rpx;
  font-weight: bold;
  color: #FFD700;
  text-shadow: 0 0 10rpx rgba(255, 215, 0, 0.6);
}

/* 推荐项目卡片 */
.recommended-project-card {
  background: rgba(255, 255, 255, 0.08);
  border-radius: 20rpx;
  padding: 25rpx;
  backdrop-filter: blur(15rpx);
  border: 1rpx solid rgba(255, 215, 0, 0.4);
  transition: all 0.3s ease;
}

.recommended-project-card:active {
  transform: scale(0.98);
  background: rgba(255, 255, 255, 0.12);
}

.project-info {
  display: flex;
  align-items: center;
  margin-bottom: 20rpx;
}

.project-icon {
  font-size: 50rpx;
  margin-right: 20rpx;
  animation: projectIconBounce 3s infinite ease-in-out;
}

@keyframes projectIconBounce {
  0%, 100% {
    transform: translateY(0);
  }
  50% {
    transform: translateY(-5rpx);
  }
}

.project-details {
  flex: 1;
}

.project-title {
  font-size: 28rpx;
  font-weight: bold;
  color: #E6E6FA;
  margin-bottom: 10rpx;
  text-shadow: 0 0 8rpx rgba(230, 230, 250, 0.4);
}

.project-progress {
  display: flex;
  align-items: center;
}

.progress-bar {
  flex: 1;
  height: 8rpx;
  background: rgba(255, 215, 0, 0.2);
  border-radius: 4rpx;
  overflow: hidden;
  margin-right: 15rpx;
}

.progress-fill {
  height: 100%;
  background: linear-gradient(90deg, #FFD700, #FFA500);
  border-radius: 4rpx;
  transition: width 0.5s ease;
  box-shadow: 0 0 8rpx rgba(255, 215, 0, 0.6);
}

.progress-text {
  font-size: 20rpx;
  color: #DDA0DD;
  white-space: nowrap;
}

.quick-donate-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #FFD700, #FFA500);
  border-radius: 15rpx;
  padding: 15rpx 25rpx;
  color: #2E1065;
  font-weight: bold;
  box-shadow: 0 4rpx 16rpx rgba(255, 215, 0, 0.4);
}

.btn-text {
  font-size: 26rpx;
  margin-right: 10rpx;
}

.btn-cost {
  font-size: 24rpx;
  opacity: 0.9;
}

/* 加载状态 */
.loading-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(15, 12, 41, 0.9);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  backdrop-filter: blur(10rpx);
}

.loading-spinner {
  text-align: center;
}

.spinner-heart {
  font-size: 80rpx;
  animation: spinnerGalaxy 2s infinite ease-in-out;
}

@keyframes spinnerGalaxy {
  0% {
    transform: rotate(0deg) scale(1);
    filter: drop-shadow(0 0 20rpx rgba(255, 215, 0, 0.8));
  }
  50% {
    transform: rotate(180deg) scale(1.3);
    filter: drop-shadow(0 0 40rpx rgba(255, 20, 147, 1));
  }
  100% {
    transform: rotate(360deg) scale(1);
    filter: drop-shadow(0 0 20rpx rgba(255, 215, 0, 0.8));
  }
}

.loading-text {
  font-size: 28rpx;
  color: #FFD700;
  margin-top: 20rpx;
  font-weight: bold;
  text-shadow: 0 0 10rpx rgba(255, 215, 0, 0.6);
}

/* 庆祝动画 */
.celebration-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(15, 12, 41, 0.95);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1001;
  backdrop-filter: blur(15rpx);
}

.celebration-content {
  text-align: center;
  position: relative;
}

.celebration-icon {
  font-size: 120rpx;
  margin-bottom: 30rpx;
  animation: celebrationGalaxyBurst 1.2s ease-out;
}

@keyframes celebrationGalaxyBurst {
  0% {
    transform: scale(0) rotate(0deg);
    filter: drop-shadow(0 0 0 rgba(255, 215, 0, 0));
  }
  30% {
    transform: scale(1.5) rotate(180deg);
    filter: drop-shadow(0 0 40rpx rgba(255, 215, 0, 1));
  }
  60% {
    transform: scale(0.8) rotate(270deg);
    filter: drop-shadow(0 0 60rpx rgba(255, 20, 147, 1));
  }
  100% {
    transform: scale(1) rotate(360deg);
    filter: drop-shadow(0 0 30rpx rgba(138, 43, 226, 0.8));
  }
}

.celebration-message {
  font-size: 36rpx;
  font-weight: bold;
  color: #FFD700;
  margin-bottom: 40rpx;
  text-shadow: 0 0 20rpx rgba(255, 215, 0, 0.8);
  animation: messageGlow 2s infinite ease-in-out;
}

@keyframes messageGlow {
  0%, 100% {
    text-shadow: 0 0 20rpx rgba(255, 215, 0, 0.8);
  }
  50% {
    text-shadow: 0 0 40rpx rgba(255, 215, 0, 1), 0 0 60rpx rgba(255, 20, 147, 0.6);
  }
}

.celebration-hearts {
  position: absolute;
  top: -100rpx;
  left: 50%;
  transform: translateX(-50%);
  width: 400rpx;
  height: 400rpx;
}

.celebration-heart {
  position: absolute;
  font-size: 40rpx;
  animation: celebrationStarBurst 3s ease-out forwards;
}

.celebration-heart.heart-1 {
  top: 50%;
  left: 50%;
  animation-delay: 0.3s;
}

.celebration-heart.heart-2 {
  top: 20%;
  left: 20%;
  animation-delay: 0.6s;
}

.celebration-heart.heart-3 {
  top: 20%;
  right: 20%;
  animation-delay: 0.9s;
}

.celebration-heart.heart-4 {
  bottom: 20%;
  left: 20%;
  animation-delay: 1.2s;
}

.celebration-heart.heart-5 {
  bottom: 20%;
  right: 20%;
  animation-delay: 1.5s;
}

@keyframes celebrationStarBurst {
  0% {
    transform: translate(-50%, -50%) scale(0) rotate(0deg);
    opacity: 1;
    filter: drop-shadow(0 0 0 rgba(255, 215, 0, 0));
  }
  20% {
    transform: translate(-50%, -50%) scale(1.5) rotate(90deg);
    opacity: 1;
    filter: drop-shadow(0 0 30rpx rgba(255, 215, 0, 1));
  }
  50% {
    transform: translate(-50%, -50%) scale(1.2) rotate(180deg);
    opacity: 0.8;
    filter: drop-shadow(0 0 40rpx rgba(255, 20, 147, 1));
  }
  80% {
    transform: translate(-50%, -200rpx) scale(0.8) rotate(270deg);
    opacity: 0.4;
    filter: drop-shadow(0 0 20rpx rgba(138, 43, 226, 0.8));
  }
  100% {
    transform: translate(-50%, -300rpx) scale(0.3) rotate(360deg);
    opacity: 0;
    filter: drop-shadow(0 0 0 rgba(138, 43, 226, 0));
  }
}






