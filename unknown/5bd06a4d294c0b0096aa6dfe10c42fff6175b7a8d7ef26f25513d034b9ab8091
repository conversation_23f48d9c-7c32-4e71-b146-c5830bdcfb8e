// 《能量星球》AI分析系统

/**
 * AI分析引擎
 */
const AIAnalysisEngine = {
  storageKey: 'aiAnalysisData',
  gameDataKey: 'gamePerformanceData',

  /**
   * 生成综合能力分析报告
   */
  generateAnalysisReport(childData, gameData) {
    // 如果没有游戏数据，生成模拟数据
    if (!gameData || gameData.length === 0) {
      gameData = this.generateMockGameData();
    }
    
    const abilities = this.analyzeAbilities(childData, gameData);
    const trends = this.analyzeTrends(gameData);
    const suggestions = this.generateSuggestions(abilities, trends);
    const weakPoints = this.identifyWeakPoints(abilities);
    
    const report = {
      timestamp: Date.now(),
      abilities,
      trends,
      suggestions,
      weakPoints,
      overallScore: this.calculateOverallScore(abilities),
      progress: this.calculateAnalysisProgress(gameData)
    };

    // 保存分析结果
    this.saveAnalysisReport(report);
    
    return report;
  },

  /**
   * 分析各项能力
   */
  analyzeAbilities(childData, gameData) {
    const abilities = {
      logic: this.analyzeLogicAbility(childData, gameData),
      creativity: this.analyzeCreativityAbility(childData, gameData),
      memory: this.analyzeMemoryAbility(childData, gameData),
      empathy: this.analyzeEmpathyAbility(childData, gameData),
      problemSolving: this.analyzeProblemSolvingAbility(childData, gameData),
      attention: this.analyzeAttentionAbility(childData, gameData)
    };

    return abilities;
  },

  /**
   * 分析趋势数据
   */
  analyzeTrends(gameData) {
    if (!gameData || gameData.length < 2) {
      return {
        overall: 'stable',
        weekly: 'stable',
        recent: 'stable'
      };
    }

    return {
      overall: 'stable',
      weekly: 'stable',
      recent: 'stable',
      dataPoints: gameData.length
    };
  },

  /**
   * 分析逻辑思维能力
   */
  analyzeLogicAbility(childData, gameData) {
    let baseScore = 50;
    
    // 基于智慧能量的基础评分
    const energyScore = Math.min(40, (childData.wisdomEnergy / 200) * 40);
    baseScore += energyScore;
    
    // 基于逻辑类游戏表现
    const logicGames = gameData.filter(g => g.type === 'logic');
    if (logicGames.length > 0) {
      const avgAccuracy = logicGames.reduce((sum, g) => sum + g.accuracy, 0) / logicGames.length;
      const avgSpeed = logicGames.reduce((sum, g) => sum + g.speed, 0) / logicGames.length;
      
      baseScore += (avgAccuracy - 0.5) * 30;
      baseScore += Math.max(0, (1 - avgSpeed) * 20);
    }
    
    const trend = this.calculateTrend(logicGames, 'accuracy');
    
    return {
      score: Math.max(0, Math.min(100, Math.round(baseScore))),
      trend,
      lastUpdate: Date.now(),
      gameCount: logicGames.length,
      confidence: this.calculateConfidence(logicGames.length)
    };
  },

  /**
   * 分析创造力
   */
  analyzeCreativityAbility(childData, gameData) {
    let baseScore = 50;
    
    const energyScore = Math.min(35, ((childData.wisdomEnergy + childData.loveEnergy) / 350) * 35);
    baseScore += energyScore;
    
    const creativityGames = gameData.filter(g => g.type === 'creativity');
    if (creativityGames.length > 0) {
      const avgOriginality = creativityGames.reduce((sum, g) => sum + (g.originality || 0.7), 0) / creativityGames.length;
      const avgEngagement = creativityGames.reduce((sum, g) => sum + (g.engagement || 0.8), 0) / creativityGames.length;
      
      baseScore += (avgOriginality - 0.5) * 25;
      baseScore += (avgEngagement - 0.5) * 20;
    }
    
    const trend = this.calculateTrend(creativityGames, 'originality');
    
    return {
      score: Math.max(0, Math.min(100, Math.round(baseScore))),
      trend,
      lastUpdate: Date.now(),
      gameCount: creativityGames.length,
      confidence: this.calculateConfidence(creativityGames.length)
    };
  },

  /**
   * 分析记忆能力
   */
  analyzeMemoryAbility(childData, gameData) {
    let baseScore = 50;
    
    const energyScore = Math.min(35, (childData.wisdomEnergy / 150) * 35);
    baseScore += energyScore;
    
    const memoryGames = gameData.filter(g => g.type === 'memory');
    if (memoryGames.length > 0) {
      const avgAccuracy = memoryGames.reduce((sum, g) => sum + g.accuracy, 0) / memoryGames.length;
      const avgRetention = memoryGames.reduce((sum, g) => sum + (g.retention || 0.8), 0) / memoryGames.length;
      
      baseScore += (avgAccuracy - 0.5) * 30;
      baseScore += (avgRetention - 0.5) * 20;
    }
    
    const trend = this.calculateTrend(memoryGames, 'accuracy');
    
    return {
      score: Math.max(0, Math.min(100, Math.round(baseScore))),
      trend,
      lastUpdate: Date.now(),
      gameCount: memoryGames.length,
      confidence: this.calculateConfidence(memoryGames.length)
    };
  },

  /**
   * 分析共情能力
   */
  analyzeEmpathyAbility(childData, gameData) {
    let baseScore = 50;
    
    const energyScore = Math.min(40, (childData.loveEnergy / 150) * 40);
    baseScore += energyScore;
    
    const socialGames = gameData.filter(g => g.type === 'social' || g.type === 'empathy');
    if (socialGames.length > 0) {
      const avgCooperation = socialGames.reduce((sum, g) => sum + (g.cooperation || 0.7), 0) / socialGames.length;
      const avgKindness = socialGames.reduce((sum, g) => sum + (g.kindness || 0.8), 0) / socialGames.length;
      
      baseScore += (avgCooperation - 0.5) * 25;
      baseScore += (avgKindness - 0.5) * 25;
    }
    
    const trend = this.calculateTrend(socialGames, 'cooperation');
    
    return {
      score: Math.max(0, Math.min(100, Math.round(baseScore))),
      trend,
      lastUpdate: Date.now(),
      gameCount: socialGames.length,
      confidence: this.calculateConfidence(socialGames.length)
    };
  },

  /**
   * 分析问题解决能力
   */
  analyzeProblemSolvingAbility(childData, gameData) {
    let baseScore = 50;
    
    const totalEnergy = childData.wisdomEnergy + childData.loveEnergy;
    const energyScore = Math.min(30, (totalEnergy / 300) * 30);
    baseScore += energyScore;
    
    const puzzleGames = gameData.filter(g => g.type === 'puzzle' || g.type === 'logic');
    if (puzzleGames.length > 0) {
      const avgSolutionTime = puzzleGames.reduce((sum, g) => sum + (g.solutionTime || 1), 0) / puzzleGames.length;
      const avgHintsUsed = puzzleGames.reduce((sum, g) => sum + (g.hintsUsed || 0), 0) / puzzleGames.length;
      
      baseScore += Math.max(0, (2 - avgSolutionTime) * 15);
      baseScore += Math.max(0, (1 - avgHintsUsed) * 25);
    }
    
    const trend = this.calculateTrend(puzzleGames, 'solutionTime', true);
    
    return {
      score: Math.max(0, Math.min(100, Math.round(baseScore))),
      trend,
      lastUpdate: Date.now(),
      gameCount: puzzleGames.length,
      confidence: this.calculateConfidence(puzzleGames.length)
    };
  },

  /**
   * 分析注意力
   */
  analyzeAttentionAbility(childData, gameData) {
    let baseScore = 50;
    
    if (gameData.length > 0) {
      const avgFocusTime = gameData.reduce((sum, g) => sum + (g.focusTime || 5), 0) / gameData.length;
      const avgInterruptions = gameData.reduce((sum, g) => sum + (g.interruptions || 0), 0) / gameData.length;
      
      baseScore += Math.min(30, (avgFocusTime / 10) * 30);
      baseScore += Math.max(0, (1 - avgInterruptions) * 20);
    }
    
    const trend = this.calculateTrend(gameData, 'focusTime');
    
    return {
      score: Math.max(0, Math.min(100, Math.round(baseScore))),
      trend,
      lastUpdate: Date.now(),
      gameCount: gameData.length,
      confidence: this.calculateConfidence(gameData.length)
    };
  },

  /**
   * 计算趋势
   */
  calculateTrend(gameData, metric, reverse = false) {
    if (gameData.length < 2) return 'stable';
    
    const recent = gameData.slice(-5);
    const older = gameData.slice(-10, -5);
    
    if (older.length === 0) return 'stable';
    
    const recentAvg = recent.reduce((sum, g) => sum + (g[metric] || 0), 0) / recent.length;
    const olderAvg = older.reduce((sum, g) => sum + (g[metric] || 0), 0) / older.length;
    
    const diff = reverse ? olderAvg - recentAvg : recentAvg - olderAvg;
    
    if (diff > 0.1) return 'up';
    if (diff < -0.1) return 'down';
    return 'stable';
  },

  /**
   * 计算置信度
   */
  calculateConfidence(gameCount) {
    if (gameCount >= 10) return 'high';
    if (gameCount >= 5) return 'medium';
    return 'low';
  },

  /**
   * 生成个性化建议
   */
  generateSuggestions(abilities, trends) {
    const suggestions = [];

    // 基于能力分数生成建议
    Object.entries(abilities).forEach(([ability, data]) => {
      if (data.score < 60) {
        suggestions.push({
          type: 'improvement',
          ability,
          priority: data.score < 40 ? 'high' : 'medium',
          suggestion: this.getImprovementSuggestion(ability),
          confidence: data.confidence
        });
      }
    });

    return suggestions.slice(0, 5);
  },

  /**
   * 获取改进建议
   */
  getImprovementSuggestion(ability) {
    const suggestions = {
      logic: '建议增加逻辑推理类游戏，如数独、编程启蒙等',
      creativity: '鼓励参与创意类活动，如绘画、故事创作、搭建游戏',
      memory: '可以尝试记忆类游戏，如卡片记忆、序列记忆等',
      empathy: '建议参与更多合作类游戏和慈善活动',
      problemSolving: '推荐解谜类游戏，培养分析和解决问题的能力',
      attention: '建议设置专注时间，减少干扰因素'
    };

    return suggestions[ability] || '建议多样化学习，均衡发展各项能力';
  },

  /**
   * 识别薄弱点
   */
  identifyWeakPoints(abilities) {
    return Object.entries(abilities)
      .filter(([_, data]) => data.score < 60)
      .sort((a, b) => a[1].score - b[1].score)
      .slice(0, 3)
      .map(([ability, data]) => ({
        ability,
        score: data.score,
        severity: data.score < 40 ? 'high' : 'medium'
      }));
  },

  /**
   * 计算总体评分
   */
  calculateOverallScore(abilities) {
    const scores = Object.values(abilities).map(a => a.score);
    return Math.round(scores.reduce((sum, score) => sum + score, 0) / scores.length);
  },

  /**
   * 计算分析进度
   */
  calculateAnalysisProgress(gameData) {
    const totalGames = gameData.length;
    const minGamesForFullAnalysis = 20;

    return Math.min(100, Math.round((totalGames / minGamesForFullAnalysis) * 100));
  },

  /**
   * 保存分析报告
   */
  saveAnalysisReport(report) {
    wx.setStorageSync(this.storageKey, report);
  },

  /**
   * 获取最新分析报告
   */
  getLatestReport() {
    return wx.getStorageSync(this.storageKey) || null;
  },

  /**
   * 模拟游戏数据（用于演示）
   */
  generateMockGameData() {
    const gameTypes = ['logic', 'creativity', 'memory', 'social', 'puzzle'];
    const mockData = [];

    for (let i = 0; i < 15; i++) {
      const type = gameTypes[Math.floor(Math.random() * gameTypes.length)];
      mockData.push({
        id: `game_${i}`,
        type,
        timestamp: Date.now() - (i * 24 * 60 * 60 * 1000),
        accuracy: 0.5 + Math.random() * 0.4,
        speed: 0.3 + Math.random() * 0.7,
        focusTime: 3 + Math.random() * 7,
        interruptions: Math.floor(Math.random() * 3),
        originality: 0.4 + Math.random() * 0.5,
        engagement: 0.6 + Math.random() * 0.4,
        cooperation: 0.5 + Math.random() * 0.5,
        kindness: 0.6 + Math.random() * 0.4,
        solutionTime: 0.5 + Math.random() * 1.5,
        hintsUsed: Math.random() * 0.5,
        retention: 0.6 + Math.random() * 0.4
      });
    }

    return mockData;
  }
};

// 导出单例
const aiAnalysisEngine = AIAnalysisEngine;

module.exports = {
  aiAnalysisEngine
};
