/* 《能量星球》地球指挥部 - 家长中心样式 */

/* 页面基础样式 */
.page.command-center {
  min-height: 100vh;
  background: radial-gradient(ellipse at center, #1A183E 0%, #0D0B1E 70%, #000000 100%);
  color: #FFFFFF;
  padding: 0;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  position: relative;
}

/* HUD 指挥部抬头显示器 */
.command-hud {
  position: relative;
  z-index: 100;
  padding: 20rpx 32rpx;
  background: linear-gradient(180deg, rgba(26, 24, 62, 0.9) 0%, rgba(26, 24, 62, 0) 100%);
  backdrop-filter: blur(15px);
  border-bottom: 1rpx solid rgba(77, 159, 255, 0.3);
}

/* 移除了状态指示器相关样式 */

/* 指挥部标题 */
.command-title {
  position: relative;
  text-align: center;
  padding: 24rpx 0;
  margin: 0 auto;
}

.title-glow {
  position: absolute;
  width: 100%;
  height: 60rpx;
  background: linear-gradient(90deg,
    rgba(77, 159, 255, 0) 0%,
    rgba(77, 159, 255, 0.3) 20%,
    rgba(255, 215, 106, 0.4) 50%,
    rgba(77, 159, 255, 0.3) 80%,
    rgba(77, 159, 255, 0) 100%);
  border-radius: 30rpx;
  filter: blur(8rpx);
  animation: title-pulse 4s infinite ease-in-out;
}

.title-text {
  position: relative;
  z-index: 10;
  font-size: 32rpx;
  font-weight: 700;
  color: #FFD76A;
  text-shadow: 0 2rpx 12rpx rgba(255, 215, 106, 0.6);
  letter-spacing: 2rpx;
  display: block;
}

.subtitle-text {
  position: relative;
  z-index: 10;
  font-size: 20rpx;
  color: #FFFFFF;
  opacity: 0.8;
  letter-spacing: 1rpx;
  margin-top: 4rpx;
  display: block;
}

/* 宇宙背景 */
.space-background {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 1;
}

/* 星空层 */
.stars-layer {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
}

.star {
  position: absolute;
  background: #FFFFFF;
  border-radius: 50%;
  animation: twinkle 3s infinite;
}

.star-1 {
  width: 4rpx;
  height: 4rpx;
  top: 25%;
  left: 20%;
  animation-delay: 0s;
}

.star-2 {
  width: 3rpx;
  height: 3rpx;
  top: 45%;
  right: 25%;
  animation-delay: 1.5s;
}

.star-3 {
  width: 5rpx;
  height: 5rpx;
  top: 70%;
  left: 30%;
  animation-delay: 3s;
}

.star-4 {
  width: 4rpx;
  height: 4rpx;
  bottom: 30%;
  right: 20%;
  animation-delay: 4.5s;
}

/* 数据流动层 */
.data-streams {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 5;
}

.data-stream {
  position: absolute;
  width: 2rpx;
  height: 100rpx;
  background: linear-gradient(180deg, 
    rgba(77, 159, 255, 0) 0%, 
    rgba(77, 159, 255, 0.8) 50%, 
    rgba(77, 159, 255, 0) 100%);
  animation: data-flow 3s infinite linear;
}

.stream-1 {
  left: 15%;
  animation-delay: 0s;
}

.stream-2 {
  left: 50%;
  animation-delay: 1s;
}

.stream-3 {
  right: 20%;
  animation-delay: 2s;
}

/* 主控制台区域 */
.main-console {
  flex: 1;
  position: relative;
  z-index: 50;
  padding: 40rpx 32rpx;
  display: grid;
  grid-template-columns: 1fr 1fr;
  grid-template-rows: 1fr 1fr 1fr;
  gap: 20rpx;
}

/* 控制台模块 */
.console-module {
  position: relative;
  background: rgba(26, 24, 62, 0.8);
  border-radius: 16rpx;
  border: 1rpx solid rgba(255, 255, 255, 0.2);
  backdrop-filter: blur(10px);
  padding: 20rpx;
  transition: all 0.3s ease;
  overflow: hidden;
}

.console-module:active {
  transform: scale(0.98);
}

/* 模块发光效果 */
.module-glow {
  position: absolute;
  top: -2rpx;
  left: -2rpx;
  right: -2rpx;
  bottom: -2rpx;
  background: linear-gradient(45deg, 
    rgba(77, 159, 255, 0.3) 0%, 
    rgba(255, 215, 106, 0.3) 50%, 
    rgba(77, 159, 255, 0.3) 100%);
  border-radius: 22rpx;
  opacity: 0;
  transition: opacity 0.3s ease;
  z-index: -1;
}

.ai-glow {
  background: linear-gradient(45deg, 
    rgba(99, 226, 183, 0.3) 0%, 
    rgba(77, 159, 255, 0.3) 100%);
}

.reward-glow {
  background: linear-gradient(45deg, 
    rgba(255, 215, 106, 0.3) 0%, 
    rgba(255, 107, 107, 0.3) 100%);
}

.settings-glow {
  background: linear-gradient(45deg,
    rgba(164, 176, 190, 0.3) 0%,
    rgba(77, 159, 255, 0.3) 100%);
}

.cooperation-glow {
  background: linear-gradient(45deg,
    rgba(255, 159, 67, 0.3) 0%,
    rgba(255, 107, 107, 0.3) 100%);
}

.reports-glow {
  background: linear-gradient(45deg,
    rgba(77, 159, 255, 0.3) 0%,
    rgba(99, 226, 183, 0.3) 100%);
}

.console-module:hover .module-glow {
  opacity: 1;
}

/* 模块头部 */
.module-header {
  display: flex;
  align-items: center;
  gap: 12rpx;
  margin-bottom: 20rpx;
}

.module-icon {
  font-size: 32rpx;
}

.module-title {
  font-size: 26rpx;
  font-weight: 600;
  color: #FFFFFF;
}

/* 模块内容 */
.module-content {
  color: #FFFFFF;
}

/* 监控台样式 */
.monitor-stats {
  display: flex;
  flex-direction: column;
  gap: 12rpx;
  margin-bottom: 16rpx;
}

.stat-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.stat-label {
  font-size: 22rpx;
  color: rgba(255, 255, 255, 0.8);
}

.stat-value {
  font-size: 24rpx;
  font-weight: 600;
  color: #63E2B7;
}

.activity-indicator {
  width: 100%;
  height: 8rpx;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 4rpx;
  overflow: hidden;
}

.activity-bar {
  height: 100%;
  background: linear-gradient(90deg, #63E2B7 0%, #4D9FFF 100%);
  border-radius: 4rpx;
  transition: width 0.5s ease;
}

/* AI分析样式 */
.ability-preview {
  display: flex;
  flex-direction: column;
  gap: 10rpx;
  margin-bottom: 16rpx;
}

.ability-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.ability-name {
  font-size: 20rpx;
  color: rgba(255, 255, 255, 0.8);
  flex: 1;
}

.ability-progress {
  width: 80rpx;
  height: 6rpx;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 3rpx;
  overflow: hidden;
  margin-left: 12rpx;
}

.progress-bar {
  height: 100%;
  background: linear-gradient(90deg, #4D9FFF 0%, #63E2B7 100%);
  border-radius: 3rpx;
  transition: width 0.5s ease;
}

.ai-status {
  text-align: center;
}

.ai-text {
  font-size: 20rpx;
  color: #63E2B7;
}

/* 奖励管理样式 */
.reward-summary {
  display: flex;
  justify-content: space-around;
  margin-bottom: 16rpx;
}

.reward-stat {
  text-align: center;
}

.reward-count {
  display: block;
  font-size: 28rpx;
  font-weight: 700;
  color: #FFD76A;
}

.reward-label {
  font-size: 20rpx;
  color: rgba(255, 255, 255, 0.8);
}

.reward-indicator {
  text-align: center;
}

.reward-status {
  font-size: 20rpx;
  color: #FF6B6B;
}

/* 设置预览样式 */
.settings-preview {
  display: flex;
  flex-direction: column;
  gap: 12rpx;
}

.setting-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.setting-name {
  font-size: 20rpx;
  color: rgba(255, 255, 255, 0.8);
}

.setting-value {
  font-size: 22rpx;
  font-weight: 600;
  color: #4D9FFF;
}

/* 亲子任务中心样式 */
.cooperation-preview {
  display: flex;
  flex-direction: column;
  gap: 16rpx;
}

.task-summary {
  display: flex;
  justify-content: space-around;
  margin-bottom: 12rpx;
}

.task-stat {
  text-align: center;
}

.task-count {
  display: block;
  font-size: 24rpx;
  font-weight: 700;
  color: #FF9F43;
}

.task-label {
  font-size: 18rpx;
  color: rgba(255, 255, 255, 0.8);
}

.cooperation-animation {
  position: relative;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.planet {
  width: 20rpx;
  height: 20rpx;
  border-radius: 50%;
  position: absolute;
  animation: planet-orbit 4s infinite ease-in-out;
}

.planet-parent {
  background: linear-gradient(45deg, #FF9F43 0%, #FFD76A 100%);
  left: 20%;
  animation-delay: 0s;
  box-shadow: 0 0 15rpx rgba(255, 159, 67, 0.6);
}

.planet-child {
  background: linear-gradient(45deg, #4D9FFF 0%, #63E2B7 100%);
  right: 20%;
  animation-delay: 2s;
  box-shadow: 0 0 15rpx rgba(77, 159, 255, 0.6);
}

.connection-line {
  position: absolute;
  width: 60%;
  height: 2rpx;
  background: linear-gradient(90deg,
    rgba(255, 159, 67, 0) 0%,
    rgba(255, 159, 67, 0.8) 50%,
    rgba(77, 159, 255, 0) 100%);
  animation: connection-pulse 2s infinite ease-in-out;
}

.cooperation-status {
  text-align: center;
}

.status-text {
  font-size: 18rpx;
  color: #FF9F43;
}

/* 学习报告生成样式 */
.reports-preview {
  display: flex;
  flex-direction: column;
  gap: 16rpx;
  position: relative;
}

.chart-container {
  display: flex;
  align-items: end;
  justify-content: space-around;
  height: 50rpx;
  margin-bottom: 12rpx;
}

.chart-line {
  width: 8rpx;
  background: linear-gradient(180deg, #4D9FFF 0%, #63E2B7 100%);
  border-radius: 4rpx;
  animation: chart-grow 1s ease-out forwards;
  transform-origin: bottom;
  transform: scaleY(0);
  box-shadow: 0 0 10rpx rgba(77, 159, 255, 0.4);
}

.report-summary {
  display: flex;
  justify-content: space-between;
}

.summary-item {
  text-align: center;
  flex: 1;
}

.summary-label {
  display: block;
  font-size: 18rpx;
  color: rgba(255, 255, 255, 0.8);
  margin-bottom: 4rpx;
}

.summary-value {
  font-size: 20rpx;
  font-weight: 600;
  color: #63E2B7;
}

.data-flow {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  pointer-events: none;
}

.data-particle {
  position: absolute;
  width: 4rpx;
  height: 4rpx;
  background: #4D9FFF;
  border-radius: 50%;
  animation: data-particle-flow 3s infinite linear;
  opacity: 0;
}

.data-particle:nth-child(1) {
  left: 10%;
  animation-delay: 0s;
}

.data-particle:nth-child(2) {
  left: 30%;
  animation-delay: 0.6s;
}

.data-particle:nth-child(3) {
  left: 50%;
  animation-delay: 1.2s;
}

.data-particle:nth-child(4) {
  left: 70%;
  animation-delay: 1.8s;
}

.data-particle:nth-child(5) {
  left: 90%;
  animation-delay: 2.4s;
}

/* 底部控制栏 */
.bottom-controls {
  position: relative;
  z-index: 100;
  display: flex;
  justify-content: space-around;
  padding: 24rpx 32rpx;
  background: linear-gradient(0deg, rgba(26, 24, 62, 0.9) 0%, rgba(26, 24, 62, 0) 100%);
  backdrop-filter: blur(15px);
  border-top: 1rpx solid rgba(77, 159, 255, 0.3);
}

.control-button {
  position: relative;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8rpx;
  padding: 16rpx 24rpx;
  background: rgba(26, 24, 62, 0.8);
  border-radius: 16rpx;
  border: 1rpx solid rgba(255, 255, 255, 0.2);
  transition: all 0.3s ease;
  overflow: hidden;
}

.control-button:active {
  transform: scale(0.95);
}

.button-content {
  position: relative;
  z-index: 10;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8rpx;
  transition: all 0.3s ease;
}

.control-icon {
  font-size: 28rpx;
}

.control-text {
  font-size: 20rpx;
  color: #FFFFFF;
}

/* 超级火箭引擎效果 */
.rocket-engine {
  position: absolute;
  bottom: -20rpx;
  left: 50%;
  transform: translateX(-50%);
  width: 100rpx;
  height: 60rpx;
  opacity: 0;
  z-index: 5;
}

.flame-layer {
  position: absolute;
  bottom: 0;
  left: 50%;
  transform: translateX(-50%);
}

.main-flame {
  width: 80rpx;
  height: 50rpx;
  z-index: 6;
}

.sub-flame {
  width: 60rpx;
  height: 35rpx;
  z-index: 7;
}

.flame-particle {
  position: absolute;
  border-radius: 50% 50% 50% 50% / 60% 60% 40% 40%;
  opacity: 0;
}

.main-flame .flame-particle {
  width: 8rpx;
  height: 16rpx;
  background: radial-gradient(ellipse at center, #00BFFF 0%, #1E90FF 30%, #FF4500 60%, #FFD700 100%);
  box-shadow: 0 0 10rpx #00BFFF, 0 0 20rpx #1E90FF, 0 0 30rpx #FF4500;
}

.sub-flame .flame-particle {
  width: 6rpx;
  height: 12rpx;
  background: radial-gradient(ellipse at center, #00FFFF 0%, #0080FF 40%, #FF6600 70%, #FFFF00 100%);
  box-shadow: 0 0 8rpx #00FFFF, 0 0 15rpx #0080FF;
}

.ion-thruster {
  position: absolute;
  bottom: 10rpx;
  left: 50%;
  transform: translateX(-50%);
  width: 40rpx;
  height: 30rpx;
  z-index: 8;
}

.ion-beam {
  position: absolute;
  bottom: 0;
  left: 50%;
  transform: translateX(-50%);
  width: 4rpx;
  height: 25rpx;
  background: linear-gradient(180deg, #00FFFF 0%, #0080FF 50%, transparent 100%);
  box-shadow: 0 0 15rpx #00FFFF, 0 0 25rpx #0080FF;
  opacity: 0;
}

.ion-core {
  position: absolute;
  bottom: 20rpx;
  left: 50%;
  transform: translateX(-50%);
  width: 12rpx;
  height: 12rpx;
  background: radial-gradient(circle, #FFFFFF 0%, #00FFFF 50%, #0080FF 100%);
  border-radius: 50%;
  box-shadow: 0 0 20rpx #00FFFF, 0 0 30rpx #0080FF, 0 0 40rpx #FFFFFF;
  opacity: 0;
}

.shock-wave {
  position: absolute;
  bottom: 15rpx;
  left: 50%;
  transform: translateX(-50%);
  width: 60rpx;
  height: 20rpx;
  border: 2rpx solid #00FFFF;
  border-radius: 50%;
  opacity: 0;
  z-index: 4;
}

.space-distortion {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 120rpx;
  height: 120rpx;
  opacity: 0;
  z-index: 2;
}

.distortion-ring {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  border: 1rpx solid rgba(0, 191, 255, 0.3);
  border-radius: 50%;
  opacity: 0;
}

.distortion-ring:nth-child(1) {
  width: 40rpx;
  height: 40rpx;
  animation-delay: 0s;
}

.distortion-ring:nth-child(2) {
  width: 70rpx;
  height: 70rpx;
  animation-delay: 0.2s;
}

.distortion-ring:nth-child(3) {
  width: 100rpx;
  height: 100rpx;
  animation-delay: 0.4s;
}

.energy-shield {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 100%;
  height: 100%;
  border: 2rpx solid #00BFFF;
  border-radius: 16rpx;
  opacity: 0;
  z-index: 1;
  box-shadow: 0 0 20rpx #00BFFF, inset 0 0 20rpx rgba(0, 191, 255, 0.2);
}

.rocket-icon {
  filter: drop-shadow(0 0 10rpx #00BFFF);
}

/* 返回按钮动画状态 */
.control-button.back.animating {
  animation: rocket-vibration 0.8s ease-out forwards;
}

.control-button.back.animating .rocket-engine {
  opacity: 1;
}

.control-button.back.animating .main-flame .flame-particle {
  animation: main-flame-burst 0.8s ease-out forwards;
}

.control-button.back.animating .sub-flame .flame-particle {
  animation: sub-flame-burst 0.8s ease-out forwards;
}

.control-button.back.animating .ion-beam {
  animation: ion-beam-charge 0.8s ease-out forwards;
}

.control-button.back.animating .ion-core {
  animation: ion-core-ignite 0.8s ease-out forwards;
}

.control-button.back.animating .shock-wave {
  animation: shock-wave-expand 0.8s ease-out forwards;
}

.control-button.back.animating .distortion-ring {
  animation: space-distort 0.8s ease-out forwards;
}

.control-button.back.animating .energy-shield {
  animation: shield-activate 0.8s ease-out forwards;
}

.control-button.back.animating .button-content {
  animation: rocket-launch-sequence 0.8s ease-out forwards;
}

/* 量子数据传输效果 */
.quantum-system {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  opacity: 0;
  z-index: 5;
  overflow: hidden;
}

.hologram-grid {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 100rpx;
  height: 100rpx;
  z-index: 6;
}

.grid-line {
  position: absolute;
  background: linear-gradient(90deg, transparent 0%, #00FFFF 50%, transparent 100%);
  opacity: 0;
  box-shadow: 0 0 5rpx #00FFFF;
}

.grid-line.horizontal {
  width: 100%;
  height: 1rpx;
  left: 0;
}

.grid-line.vertical {
  width: 1rpx;
  height: 100%;
  top: 0;
}

.grid-line.horizontal:nth-child(1) { top: 0%; animation-delay: 0s; }
.grid-line.horizontal:nth-child(2) { top: 25%; animation-delay: 0.1s; }
.grid-line.horizontal:nth-child(3) { top: 50%; animation-delay: 0.2s; }
.grid-line.horizontal:nth-child(4) { top: 75%; animation-delay: 0.1s; }
.grid-line.horizontal:nth-child(5) { top: 100%; animation-delay: 0s; }

.grid-line.vertical:nth-child(6) { left: 0%; animation-delay: 0.05s; }
.grid-line.vertical:nth-child(7) { left: 25%; animation-delay: 0.15s; }
.grid-line.vertical:nth-child(8) { left: 50%; animation-delay: 0.25s; }
.grid-line.vertical:nth-child(9) { left: 75%; animation-delay: 0.15s; }
.grid-line.vertical:nth-child(10) { left: 100%; animation-delay: 0.05s; }

.data-waterfall {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 7;
}

.data-stream {
  position: absolute;
  top: -20rpx;
  width: 8rpx;
  height: 100%;
  opacity: 0;
}

.data-stream:nth-child(1) { left: 15%; animation-delay: 0s; }
.data-stream:nth-child(2) { left: 30%; animation-delay: 0.2s; }
.data-stream:nth-child(3) { left: 45%; animation-delay: 0.4s; }
.data-stream:nth-child(4) { left: 60%; animation-delay: 0.1s; }
.data-stream:nth-child(5) { left: 75%; animation-delay: 0.3s; }
.data-stream:nth-child(6) { left: 90%; animation-delay: 0.5s; }

.data-bit {
  position: absolute;
  width: 6rpx;
  height: 8rpx;
  background: #00FF00;
  color: #00FF00;
  font-size: 8rpx;
  line-height: 8rpx;
  text-align: center;
  border-radius: 2rpx;
  box-shadow: 0 0 8rpx #00FF00;
  opacity: 0;
}

.quantum-particles {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 8;
}

.quantum-dot {
  position: absolute;
  width: 4rpx;
  height: 4rpx;
  background: radial-gradient(circle, #FFFFFF 0%, #00FFFF 50%, #0080FF 100%);
  border-radius: 50%;
  box-shadow: 0 0 10rpx #00FFFF, 0 0 20rpx #0080FF;
  opacity: 0;
}

.energy-core {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 60rpx;
  height: 60rpx;
  z-index: 9;
}

.core-ring {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  border: 2rpx solid #00FFFF;
  border-radius: 50%;
  opacity: 0;
  box-shadow: 0 0 15rpx #00FFFF, inset 0 0 15rpx rgba(0, 255, 255, 0.3);
}

.core-ring:nth-child(1) {
  width: 20rpx;
  height: 20rpx;
  animation-delay: 0s;
}

.core-ring:nth-child(2) {
  width: 35rpx;
  height: 35rpx;
  animation-delay: 0.1s;
}

.core-ring:nth-child(3) {
  width: 50rpx;
  height: 50rpx;
  animation-delay: 0.2s;
}

.core-ring:nth-child(4) {
  width: 65rpx;
  height: 65rpx;
  animation-delay: 0.3s;
}

.quantum-field {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 100%;
  height: 100%;
  border: 2rpx solid #00FFFF;
  border-radius: 16rpx;
  opacity: 0;
  z-index: 1;
  box-shadow: 0 0 25rpx #00FFFF, inset 0 0 25rpx rgba(0, 255, 255, 0.2);
}

.refresh-icon {
  filter: drop-shadow(0 0 10rpx #00FFFF);
}

/* 刷新按钮动画状态 */
.control-button.refresh.animating .quantum-system {
  opacity: 1;
}

.control-button.refresh.animating .grid-line {
  animation: hologram-scan 1.2s ease-in-out forwards;
}

.control-button.refresh.animating .data-stream {
  animation: data-cascade 1.2s ease-in-out forwards;
}

.control-button.refresh.animating .data-bit {
  animation: data-bit-flow 1.2s ease-in-out forwards;
}

.control-button.refresh.animating .quantum-dot {
  animation: quantum-fluctuation 1.2s ease-in-out forwards;
}

.control-button.refresh.animating .core-ring {
  animation: energy-pulse 1.2s ease-in-out forwards;
}

.control-button.refresh.animating .quantum-field {
  animation: quantum-field-activate 1.2s ease-in-out forwards;
}

.control-button.refresh.animating .button-content {
  animation: quantum-refresh 1.2s ease-in-out forwards;
}

/* 动画定义 */

@keyframes title-pulse {
  0%, 100% { opacity: 0.8; transform: scaleX(1); }
  50% { opacity: 1; transform: scaleX(1.02); }
}

@keyframes twinkle {
  0%, 100% { opacity: 0.3; }
  50% { opacity: 1; }
}

@keyframes data-flow {
  0% { transform: translateY(-100rpx); opacity: 0; }
  10% { opacity: 1; }
  90% { opacity: 1; }
  100% { transform: translateY(100vh); opacity: 0; }
}

@keyframes planet-orbit {
  0%, 100% { transform: translateY(0px) scale(1); }
  25% { transform: translateY(-8rpx) scale(1.1); }
  50% { transform: translateY(-4rpx) scale(1.05); }
  75% { transform: translateY(-12rpx) scale(1.15); }
}

@keyframes connection-pulse {
  0%, 100% { opacity: 0.6; transform: scaleX(1); }
  50% { opacity: 1; transform: scaleX(1.1); }
}

@keyframes chart-grow {
  0% { transform: scaleY(0); opacity: 0; }
  50% { opacity: 1; }
  100% { transform: scaleY(1); opacity: 1; }
}

@keyframes data-particle-flow {
  0% {
    transform: translateY(100%);
    opacity: 0;
  }
  10% {
    opacity: 1;
  }
  50% {
    opacity: 0.8;
    transform: translateY(50%);
  }
  90% {
    opacity: 1;
  }
  100% {
    transform: translateY(-20rpx);
    opacity: 0;
  }
}

/* 超级火箭引擎动画 */
@keyframes rocket-vibration {
  0%, 100% { transform: translateX(0); }
  10% { transform: translateX(-1rpx); }
  20% { transform: translateX(1rpx); }
  30% { transform: translateX(-1rpx); }
  40% { transform: translateX(1rpx); }
  50% { transform: translateX(-1rpx); }
  60% { transform: translateX(1rpx); }
  70% { transform: translateX(-1rpx); }
  80% { transform: translateX(1rpx); }
  90% { transform: translateX(-1rpx); }
}

@keyframes main-flame-burst {
  0% {
    opacity: 0;
    transform: translateY(0) scale(0.5);
  }
  15% {
    opacity: 1;
    transform: translateY(8rpx) scale(1.2);
  }
  40% {
    opacity: 0.9;
    transform: translateY(20rpx) scale(1.5);
  }
  70% {
    opacity: 0.6;
    transform: translateY(35rpx) scale(1.8);
  }
  100% {
    opacity: 0;
    transform: translateY(50rpx) scale(2.2);
  }
}

@keyframes sub-flame-burst {
  0% {
    opacity: 0;
    transform: translateY(0) scale(0.3);
  }
  20% {
    opacity: 1;
    transform: translateY(6rpx) scale(1);
  }
  50% {
    opacity: 0.8;
    transform: translateY(15rpx) scale(1.3);
  }
  80% {
    opacity: 0.4;
    transform: translateY(25rpx) scale(1.6);
  }
  100% {
    opacity: 0;
    transform: translateY(35rpx) scale(2);
  }
}

@keyframes ion-beam-charge {
  0% {
    opacity: 0;
    transform: translateX(-50%) scaleY(0);
  }
  30% {
    opacity: 1;
    transform: translateX(-50%) scaleY(1);
  }
  70% {
    opacity: 0.8;
    transform: translateX(-50%) scaleY(1.5);
  }
  100% {
    opacity: 0;
    transform: translateX(-50%) scaleY(2);
  }
}

@keyframes ion-core-ignite {
  0% {
    opacity: 0;
    transform: translateX(-50%) scale(0.5);
  }
  25% {
    opacity: 1;
    transform: translateX(-50%) scale(1);
  }
  50% {
    opacity: 0.9;
    transform: translateX(-50%) scale(1.3);
  }
  75% {
    opacity: 0.7;
    transform: translateX(-50%) scale(1.6);
  }
  100% {
    opacity: 0;
    transform: translateX(-50%) scale(2);
  }
}

@keyframes shock-wave-expand {
  0% {
    opacity: 0;
    transform: translateX(-50%) scale(0.5);
  }
  20% {
    opacity: 0.8;
    transform: translateX(-50%) scale(1);
  }
  60% {
    opacity: 0.4;
    transform: translateX(-50%) scale(2);
  }
  100% {
    opacity: 0;
    transform: translateX(-50%) scale(3);
  }
}

@keyframes space-distort {
  0% {
    opacity: 0;
    transform: translate(-50%, -50%) scale(0.8);
  }
  30% {
    opacity: 0.6;
    transform: translate(-50%, -50%) scale(1);
  }
  70% {
    opacity: 0.3;
    transform: translate(-50%, -50%) scale(1.5);
  }
  100% {
    opacity: 0;
    transform: translate(-50%, -50%) scale(2);
  }
}

@keyframes shield-activate {
  0% {
    opacity: 0;
    transform: translate(-50%, -50%) scale(0.9);
  }
  25% {
    opacity: 0.8;
    transform: translate(-50%, -50%) scale(1);
  }
  50% {
    opacity: 0.6;
    transform: translate(-50%, -50%) scale(1.1);
  }
  75% {
    opacity: 0.4;
    transform: translate(-50%, -50%) scale(1.2);
  }
  100% {
    opacity: 0;
    transform: translate(-50%, -50%) scale(1.3);
  }
}

@keyframes rocket-launch-sequence {
  0% {
    transform: translateY(0) scale(1);
    opacity: 1;
    filter: drop-shadow(0 0 10rpx #00BFFF);
  }
  30% {
    transform: translateY(-3rpx) scale(1.05);
    opacity: 0.9;
    filter: drop-shadow(0 0 15rpx #00BFFF);
  }
  60% {
    transform: translateY(-8rpx) scale(1.1);
    opacity: 0.7;
    filter: drop-shadow(0 0 20rpx #00BFFF);
  }
  100% {
    transform: translateY(-15rpx) scale(0.8);
    opacity: 0.2;
    filter: drop-shadow(0 0 25rpx #00BFFF);
  }
}

/* 量子数据传输动画 */
@keyframes hologram-scan {
  0% {
    opacity: 0;
    transform: scaleX(0);
  }
  20% {
    opacity: 1;
    transform: scaleX(1);
  }
  80% {
    opacity: 0.8;
    transform: scaleX(1);
  }
  100% {
    opacity: 0;
    transform: scaleX(0);
  }
}

@keyframes data-cascade {
  0% {
    opacity: 0;
    transform: translateY(-100%);
  }
  20% {
    opacity: 1;
    transform: translateY(0);
  }
  80% {
    opacity: 0.8;
    transform: translateY(50%);
  }
  100% {
    opacity: 0;
    transform: translateY(150%);
  }
}

@keyframes data-bit-flow {
  0% {
    opacity: 0;
    transform: translateY(-10rpx) scale(0.5);
  }
  25% {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
  75% {
    opacity: 0.8;
    transform: translateY(20rpx) scale(1.2);
  }
  100% {
    opacity: 0;
    transform: translateY(40rpx) scale(0.8);
  }
}

@keyframes quantum-fluctuation {
  0% {
    opacity: 0;
    transform: scale(0) rotate(0deg);
  }
  25% {
    opacity: 1;
    transform: scale(1) rotate(90deg);
  }
  50% {
    opacity: 0.8;
    transform: scale(1.5) rotate(180deg);
  }
  75% {
    opacity: 0.6;
    transform: scale(1.2) rotate(270deg);
  }
  100% {
    opacity: 0;
    transform: scale(0.5) rotate(360deg);
  }
}

@keyframes energy-pulse {
  0% {
    opacity: 0;
    transform: translate(-50%, -50%) scale(0.5);
  }
  20% {
    opacity: 0.8;
    transform: translate(-50%, -50%) scale(1);
  }
  40% {
    opacity: 1;
    transform: translate(-50%, -50%) scale(1.2);
  }
  60% {
    opacity: 0.8;
    transform: translate(-50%, -50%) scale(1.4);
  }
  80% {
    opacity: 0.4;
    transform: translate(-50%, -50%) scale(1.6);
  }
  100% {
    opacity: 0;
    transform: translate(-50%, -50%) scale(2);
  }
}

@keyframes quantum-field-activate {
  0% {
    opacity: 0;
    transform: translate(-50%, -50%) scale(0.8);
    box-shadow: 0 0 25rpx #00FFFF, inset 0 0 25rpx rgba(0, 255, 255, 0.2);
  }
  25% {
    opacity: 0.8;
    transform: translate(-50%, -50%) scale(1);
    box-shadow: 0 0 35rpx #00FFFF, inset 0 0 35rpx rgba(0, 255, 255, 0.4);
  }
  50% {
    opacity: 1;
    transform: translate(-50%, -50%) scale(1.05);
    box-shadow: 0 0 45rpx #00FFFF, inset 0 0 45rpx rgba(0, 255, 255, 0.6);
  }
  75% {
    opacity: 0.6;
    transform: translate(-50%, -50%) scale(1.1);
    box-shadow: 0 0 35rpx #00FFFF, inset 0 0 35rpx rgba(0, 255, 255, 0.4);
  }
  100% {
    opacity: 0;
    transform: translate(-50%, -50%) scale(1.2);
    box-shadow: 0 0 25rpx #00FFFF, inset 0 0 25rpx rgba(0, 255, 255, 0.2);
  }
}

@keyframes quantum-refresh {
  0% {
    transform: scale(1) rotate(0deg);
    opacity: 1;
    filter: drop-shadow(0 0 10rpx #00FFFF);
  }
  25% {
    transform: scale(1.1) rotate(90deg);
    opacity: 0.9;
    filter: drop-shadow(0 0 15rpx #00FFFF);
  }
  50% {
    transform: scale(1.2) rotate(180deg);
    opacity: 0.8;
    filter: drop-shadow(0 0 20rpx #00FFFF);
  }
  75% {
    transform: scale(1.1) rotate(270deg);
    opacity: 0.9;
    filter: drop-shadow(0 0 15rpx #00FFFF);
  }
  100% {
    transform: scale(1) rotate(360deg);
    opacity: 1;
    filter: drop-shadow(0 0 10rpx #00FFFF);
  }
}
