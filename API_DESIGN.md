# 《能量星球》API设计文档

**版本**: v1.0  
**更新时间**: 2025-01-27  
**状态**: 设计阶段

---

## 📋 API概览

### 设计原则
1. **RESTful设计**: 遵循REST架构风格
2. **统一响应格式**: 标准化的响应结构
3. **版本控制**: 支持API版本管理
4. **安全优先**: 完整的认证和授权机制
5. **儿童友好**: 特殊的儿童数据保护

### 基础信息
- **Base URL**: `https://api.energyplanet.com/v1`
- **认证方式**: JWT Token + 微信授权
- **数据格式**: JSON
- **字符编码**: UTF-8

---

## 🔐 认证和授权

### 微信小程序登录流程
```javascript
// 1. 获取微信授权码
wx.login({
  success: (res) => {
    const code = res.code;
    // 2. 发送到后端换取token
    api.auth.login({ code });
  }
});
```

### JWT Token结构
```json
{
  "header": {
    "alg": "HS256",
    "typ": "JWT"
  },
  "payload": {
    "userId": "user_123",
    "role": "child|parent",
    "exp": 1640995200,
    "iat": 1640908800
  }
}
```

### 权限级别
- **child**: 儿童用户，受限访问
- **parent**: 家长用户，管理权限
- **admin**: 管理员，完全权限

---

## 📊 统一响应格式

### 成功响应
```json
{
  "success": true,
  "code": 200,
  "message": "操作成功",
  "data": {
    // 具体数据
  },
  "timestamp": 1640908800000
}
```

### 错误响应
```json
{
  "success": false,
  "code": 400,
  "message": "请求参数错误",
  "error": {
    "type": "VALIDATION_ERROR",
    "details": "age字段必须在3-7之间"
  },
  "timestamp": 1640908800000
}
```

### 状态码定义
- **200**: 成功
- **400**: 请求错误
- **401**: 未授权
- **403**: 禁止访问
- **404**: 资源不存在
- **500**: 服务器错误

---

## 👤 用户管理API

### 用户注册/登录
```http
POST /auth/login
Content-Type: application/json

{
  "code": "wx_auth_code",
  "userInfo": {
    "nickName": "小探索者",
    "avatarUrl": "https://...",
    "age": 5
  }
}
```

### 获取用户信息
```http
GET /users/profile
Authorization: Bearer {token}

Response:
{
  "success": true,
  "data": {
    "userId": "user_123",
    "captainName": "小探索者",
    "age": 5,
    "level": 3,
    "wisdomEnergy": 150,
    "loveEnergy": 89,
    "totalPlayTime": 3600,
    "achievements": ["first_game", "energy_collector"],
    "createdAt": "2025-01-01T00:00:00Z"
  }
}
```

### 更新用户信息
```http
PUT /users/profile
Authorization: Bearer {token}
Content-Type: application/json

{
  "captainName": "超级探索者",
  "preferences": {
    "soundEnabled": true,
    "difficulty": "medium"
  }
}
```

---

## ⚡ 能量系统API

### 获取能量状态
```http
GET /energy/status
Authorization: Bearer {token}

Response:
{
  "success": true,
  "data": {
    "wisdomEnergy": {
      "current": 150,
      "max": 200,
      "regenRate": 1,
      "lastUpdate": "2025-01-27T10:00:00Z"
    },
    "loveEnergy": {
      "current": 89,
      "max": 150,
      "regenRate": 0.5,
      "lastUpdate": "2025-01-27T10:00:00Z"
    }
  }
}
```

### 消耗能量
```http
POST /energy/consume
Authorization: Bearer {token}
Content-Type: application/json

{
  "type": "wisdom|love",
  "amount": 10,
  "reason": "game_play",
  "gameId": "puzzle_001"
}
```

### 获得能量
```http
POST /energy/gain
Authorization: Bearer {token}
Content-Type: application/json

{
  "type": "wisdom|love",
  "amount": 5,
  "source": "game_completion",
  "gameId": "puzzle_001"
}
```

---

## 🎮 游戏系统API

### 获取游戏列表
```http
GET /games?category=puzzle&difficulty=easy&page=1&limit=10
Authorization: Bearer {token}

Response:
{
  "success": true,
  "data": {
    "games": [
      {
        "gameId": "puzzle_001",
        "title": "星座拼图",
        "description": "拼出美丽的星座图案",
        "category": "puzzle",
        "difficulty": "easy",
        "energyCost": {
          "wisdom": 10,
          "love": 0
        },
        "rewards": {
          "wisdom": 15,
          "love": 5
        },
        "estimatedTime": 300,
        "thumbnailUrl": "https://..."
      }
    ],
    "pagination": {
      "page": 1,
      "limit": 10,
      "total": 50,
      "totalPages": 5
    }
  }
}
```

### 开始游戏
```http
POST /games/{gameId}/start
Authorization: Bearer {token}

Response:
{
  "success": true,
  "data": {
    "sessionId": "session_123",
    "gameData": {
      "level": 1,
      "config": {...},
      "startTime": "2025-01-27T10:00:00Z"
    }
  }
}
```

### 提交游戏结果
```http
POST /games/{gameId}/complete
Authorization: Bearer {token}
Content-Type: application/json

{
  "sessionId": "session_123",
  "score": 85,
  "timeSpent": 240,
  "completed": true,
  "gameData": {
    "moves": 15,
    "hints": 2
  }
}
```

---

## 🏆 成就系统API

### 获取成就列表
```http
GET /achievements
Authorization: Bearer {token}

Response:
{
  "success": true,
  "data": {
    "achievements": [
      {
        "id": "first_game",
        "title": "初次探索",
        "description": "完成第一个游戏",
        "icon": "🌟",
        "unlocked": true,
        "unlockedAt": "2025-01-27T10:00:00Z",
        "progress": {
          "current": 1,
          "target": 1
        }
      }
    ]
  }
}
```

### 解锁成就
```http
POST /achievements/{achievementId}/unlock
Authorization: Bearer {token}
```

---

## 👨‍👩‍👧‍👦 家长中心API

### 获取儿童学习报告
```http
GET /parent/children/{childId}/report?period=week
Authorization: Bearer {parent_token}

Response:
{
  "success": true,
  "data": {
    "period": "2025-01-20 to 2025-01-27",
    "summary": {
      "totalPlayTime": 1800,
      "gamesCompleted": 12,
      "wisdomEnergyGained": 150,
      "loveEnergyGained": 80,
      "newAchievements": 3
    },
    "dailyActivity": [
      {
        "date": "2025-01-27",
        "playTime": 300,
        "gamesPlayed": 2,
        "energyGained": 25
      }
    ],
    "skillProgress": {
      "logic": 75,
      "creativity": 60,
      "empathy": 80,
      "problemSolving": 70
    }
  }
}
```

### 设置游戏时间限制
```http
PUT /parent/children/{childId}/limits
Authorization: Bearer {parent_token}
Content-Type: application/json

{
  "dailyTimeLimit": 1800,
  "sessionTimeLimit": 600,
  "allowedHours": {
    "start": "09:00",
    "end": "20:00"
  },
  "weekendExtension": 600
}
```

---

## 💝 慈善系统API

### 获取慈善项目列表
```http
GET /charity/projects?status=active
Authorization: Bearer {token}

Response:
{
  "success": true,
  "data": {
    "projects": [
      {
        "projectId": "project_001",
        "title": "为山区儿童送书籍",
        "description": "帮助山区儿童获得更多学习资源",
        "targetAmount": 10000,
        "currentAmount": 7500,
        "donorCount": 150,
        "endDate": "2025-03-01T00:00:00Z",
        "imageUrl": "https://..."
      }
    ]
  }
}
```

### 捐赠爱心能量
```http
POST /charity/donate
Authorization: Bearer {token}
Content-Type: application/json

{
  "projectId": "project_001",
  "amount": 50,
  "message": "希望小朋友们都能快乐学习！"
}
```

---

## 📊 数据分析API

### 获取学习统计
```http
GET /analytics/learning?period=month
Authorization: Bearer {token}

Response:
{
  "success": true,
  "data": {
    "period": "2025-01",
    "totalSessions": 45,
    "averageSessionTime": 420,
    "favoriteCategory": "puzzle",
    "skillImprovement": {
      "logic": 15,
      "creativity": 10,
      "empathy": 20
    },
    "weeklyTrend": [
      {"week": 1, "sessions": 8, "improvement": 5},
      {"week": 2, "sessions": 12, "improvement": 8}
    ]
  }
}
```

---

## 🔧 系统管理API

### 健康检查
```http
GET /health

Response:
{
  "success": true,
  "data": {
    "status": "healthy",
    "version": "1.0.0",
    "uptime": 86400,
    "services": {
      "database": "healthy",
      "cache": "healthy",
      "storage": "healthy"
    }
  }
}
```

### 获取系统配置
```http
GET /config
Authorization: Bearer {token}

Response:
{
  "success": true,
  "data": {
    "gameConfig": {
      "maxDailyPlayTime": 1800,
      "energyRegenRate": 1
    },
    "features": {
      "charityEnabled": true,
      "parentDashboard": true,
      "voiceInteraction": false
    }
  }
}
```

---

## 📝 错误码定义

### 业务错误码
- **1001**: 用户不存在
- **1002**: 能量不足
- **1003**: 游戏会话无效
- **1004**: 成就已解锁
- **1005**: 超出时间限制
- **2001**: 家长权限不足
- **2002**: 儿童账户受限
- **3001**: 慈善项目不存在
- **3002**: 捐赠金额无效

### 系统错误码
- **5001**: 数据库连接失败
- **5002**: 缓存服务异常
- **5003**: 第三方服务不可用
- **5004**: 配置文件错误

---

**API总结**: 当前API设计覆盖了核心业务功能，采用RESTful风格，注重安全性和儿童数据保护。后续将根据功能开发进度逐步实现各个接口。
