/* 《能量星球》全局CSS变量系统 */

/* ==================== 色彩系统 ==================== */

/* 主色调 - 高对比度太空舱设计 */
page {
  /* 背景色 */
  --space-deep: #1A183E;        /* 深空紫蓝 - 主背景 */
  --space-gradient-start: #1A183E;
  --space-gradient-end: #2D1B69;
  
  /* 功能色 */
  --energy-yellow: #FFD76A;     /* 能量黄 - 行动/高亮 */
  --wisdom-blue: #4D9FFF;       /* 智慧蓝 - 逻辑/益智 */
  --love-red: #FF6B6B;          /* 爱心红 - 社交/慈善 */
  --success-green: #63E2B7;     /* 成就绿 - 成功/反馈 */
  --star-white: #FFFFFF;        /* 星光白 - 文本 */
  
  /* 辅助色 */
  --warning-orange: #FF9F43;    /* 警告橙 */
  --info-cyan: #54A0FF;         /* 信息青 */
  --neutral-gray: #A4B0BE;      /* 中性灰 */
  
  /* 透明度变体 */
  --energy-yellow-light: rgba(255, 215, 106, 0.2);
  --wisdom-blue-light: rgba(77, 159, 255, 0.2);
  --love-red-light: rgba(255, 107, 107, 0.2);
  --success-green-light: rgba(99, 226, 183, 0.2);
  --star-white-light: rgba(255, 255, 255, 0.1);
  
  /* 发光效果 */
  --glow-energy: rgba(255, 215, 106, 0.4);
  --glow-wisdom: rgba(77, 159, 255, 0.4);
  --glow-love: rgba(255, 107, 107, 0.4);
  --glow-success: rgba(99, 226, 183, 0.4);
  
  /* 阴影 */
  --shadow-soft: rgba(0, 0, 0, 0.2);
  --shadow-medium: rgba(0, 0, 0, 0.3);
  --shadow-strong: rgba(0, 0, 0, 0.5);
}

/* ==================== 字体系统 ==================== */

page {
  /* 字体大小 - 适配儿童阅读习惯 */
  --font-size-xs: 24rpx;        /* 极小文字 */
  --font-size-sm: 28rpx;        /* 小文字 */
  --font-size-md: 32rpx;        /* 正文 */
  --font-size-lg: 36rpx;        /* 副标题 */
  --font-size-xl: 48rpx;        /* 主标题 */
  --font-size-xxl: 64rpx;       /* 超大标题 */
  
  /* 字体权重 */
  --font-weight-light: 300;
  --font-weight-normal: 400;
  --font-weight-medium: 500;
  --font-weight-semibold: 600;
  --font-weight-bold: 700;
  
  /* 行高 */
  --line-height-tight: 1.2;
  --line-height-normal: 1.4;
  --line-height-relaxed: 1.6;
  --line-height-loose: 1.8;
}

/* ==================== 间距系统 ==================== */

page {
  /* 基础间距单位 - 8rpx基准 */
  --spacing-xs: 8rpx;           /* 极小间距 */
  --spacing-sm: 16rpx;          /* 小间距 */
  --spacing-md: 24rpx;          /* 中等间距 */
  --spacing-lg: 32rpx;          /* 大间距 */
  --spacing-xl: 48rpx;          /* 超大间距 */
  --spacing-xxl: 64rpx;         /* 极大间距 */
  --spacing-xxxl: 96rpx;        /* 超级大间距 */
  
  /* 页面边距 */
  --page-padding-horizontal: 32rpx;
  --page-padding-vertical: 24rpx;
  
  /* 组件内边距 */
  --component-padding-sm: var(--spacing-sm);
  --component-padding-md: var(--spacing-md);
  --component-padding-lg: var(--spacing-lg);
}

/* ==================== 圆角系统 ==================== */

page {
  /* 圆角大小 - 友好柔和的设计 */
  --radius-xs: 4rpx;            /* 极小圆角 */
  --radius-sm: 8rpx;            /* 小圆角 */
  --radius-md: 16rpx;           /* 中等圆角 */
  --radius-lg: 24rpx;           /* 大圆角 */
  --radius-xl: 32rpx;           /* 超大圆角 */
  --radius-round: 50%;          /* 圆形 */
  --radius-pill: 999rpx;        /* 胶囊形 */
}

/* ==================== 尺寸系统 ==================== */

page {
  /* 触摸目标最小尺寸 - 儿童友好 */
  --touch-target-min: 44rpx;
  --touch-target-comfortable: 60rpx;
  --touch-target-large: 80rpx;
  
  /* 图标尺寸 */
  --icon-xs: 24rpx;
  --icon-sm: 32rpx;
  --icon-md: 48rpx;
  --icon-lg: 64rpx;
  --icon-xl: 96rpx;
  
  /* 头像尺寸 */
  --avatar-sm: 60rpx;
  --avatar-md: 80rpx;
  --avatar-lg: 120rpx;
  --avatar-xl: 160rpx;
  
  /* 按钮高度 */
  --button-height-sm: 60rpx;
  --button-height-md: 80rpx;
  --button-height-lg: 100rpx;
}

/* ==================== 动画系统 ==================== */

page {
  /* 动画时长 */
  --duration-fast: 0.15s;       /* 快速动画 */
  --duration-normal: 0.3s;      /* 正常动画 */
  --duration-slow: 0.5s;        /* 慢速动画 */
  --duration-slower: 0.8s;      /* 更慢动画 */
  
  /* 缓动函数 */
  --ease-out: cubic-bezier(0.25, 0.46, 0.45, 0.94);
  --ease-in: cubic-bezier(0.55, 0.055, 0.675, 0.19);
  --ease-in-out: cubic-bezier(0.645, 0.045, 0.355, 1);
  --ease-bounce: cubic-bezier(0.68, -0.55, 0.265, 1.55);
}

/* ==================== Z-index层级 ==================== */

page {
  /* 层级管理 */
  --z-index-dropdown: 1000;
  --z-index-sticky: 1020;
  --z-index-fixed: 1030;
  --z-index-modal-backdrop: 1040;
  --z-index-modal: 1050;
  --z-index-popover: 1060;
  --z-index-tooltip: 1070;
  --z-index-toast: 1080;
}

/* ==================== 渐变系统 ==================== */

page {
  /* 背景渐变 */
  --gradient-space: linear-gradient(135deg, var(--space-deep) 0%, var(--space-gradient-end) 100%);
  --gradient-energy: linear-gradient(135deg, var(--energy-yellow) 0%, #FFE066 100%);
  --gradient-wisdom: linear-gradient(135deg, var(--wisdom-blue) 0%, #66B3FF 100%);
  --gradient-love: linear-gradient(135deg, var(--love-red) 0%, #FF8A8A 100%);
  --gradient-success: linear-gradient(135deg, var(--success-green) 0%, #7EEBC7 100%);
  
  /* 玻璃态效果 */
  --glass-background: rgba(255, 255, 255, 0.1);
  --glass-border: rgba(255, 255, 255, 0.2);
  --glass-backdrop-filter: blur(10px);
}

/* ==================== 响应式断点 ==================== */

page {
  /* 屏幕断点 */
  --breakpoint-sm: 576rpx;      /* 小屏幕 */
  --breakpoint-md: 768rpx;      /* 中等屏幕 */
  --breakpoint-lg: 992rpx;      /* 大屏幕 */
  --breakpoint-xl: 1200rpx;     /* 超大屏幕 */
}

/* ==================== 安全区域 ==================== */

page {
  /* 安全区域适配 */
  --safe-area-inset-top: env(safe-area-inset-top);
  --safe-area-inset-right: env(safe-area-inset-right);
  --safe-area-inset-bottom: env(safe-area-inset-bottom);
  --safe-area-inset-left: env(safe-area-inset-left);
}
