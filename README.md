# 我的小程序项目

这是一个干净的微信小程序开发基础环境。

## 项目结构

```
├── miniprogram/                 # 小程序源码目录
│   ├── pages/                   # 页面目录
│   │   └── index/               # 首页
│   │       ├── index.js         # 页面逻辑
│   │       ├── index.json       # 页面配置
│   │       ├── index.wxml       # 页面结构
│   │       └── index.wxss       # 页面样式
│   ├── components/              # 自定义组件目录
│   ├── app.js                   # 小程序逻辑
│   ├── app.json                 # 小程序公共配置
│   ├── app.wxss                 # 小程序公共样式
│   └── sitemap.json             # 站点地图
├── cloudfunctions/              # 云函数目录
├── project.config.json          # 项目配置文件
└── project.private.config.json  # 项目私有配置文件
```

## 开发说明

1. 项目已清理了所有模版代码，保留了基础框架
2. 当前只有一个首页，您可以根据需要添加更多页面
3. 如需使用云开发功能，请在 `app.js` 中添加云开发初始化代码
4. 请根据实际需求修改 `project.config.json` 中的 `appid`

## 注意事项

- 请确保在微信开发者工具中正确配置项目
- 如果需要使用云开发，请先开通云开发服务
- 建议定期备份项目代码

## 下一步

现在您可以开始开发您的小程序功能了！
