// 《能量星球》宇宙灯塔慈善系统

/**
 * 慈善系统管理类
 */
class CharitySystem {
  constructor() {
    this.storageKey = 'charityData';
    this.donationKey = 'donationHistory';
    this.badgeKey = 'charityBadges';
    this.storyKey = 'charityStories';
    this.defaultProjects = this.getDefaultProjects();
    this.seasonalThemes = this.getSeasonalThemes();
  }

  /**
   * 获取默认慈善项目
   */
  getDefaultProjects() {
    return [
      {
        id: 'project_001',
        title: '小树苗成长计划',
        subtitle: '帮助虚拟森林种植1000棵树',
        description: '每一棵小树苗都承载着希望，让我们一起为地球增添绿色！',
        icon: '🌱',
        category: 'environment',
        targetAmount: 1000,
        currentAmount: 0,
        donorCount: 0,
        energyCost: 10, // 每次捐赠消耗的爱心能量
        rewardBadge: {
          name: '绿色守护者',
          icon: '🌳',
          description: '为地球种下第一棵树'
        },
        story: {
          title: '小树苗的心愿',
          content: '我是一颗小树苗，梦想着长成参天大树，为小鸟们提供温暖的家...',
          images: ['tree1.jpg', 'tree2.jpg']
        },
        season: 'spring',
        status: 'active'
      },
      {
        id: 'project_002',
        title: '山区小书屋',
        subtitle: '为偏远地区孩子建设图书馆',
        description: '知识是最好的礼物，让我们为山区的小朋友送去智慧的光芒！',
        icon: '📚',
        category: 'education',
        targetAmount: 800,
        currentAmount: 0,
        donorCount: 0,
        energyCost: 12,
        rewardBadge: {
          name: '知识传播者',
          icon: '📖',
          description: '为山区孩子送去第一本书'
        },
        story: {
          title: '小明的读书梦',
          content: '山区的小明最大的愿望就是有一个图书馆，那里有好多好多的故事书...',
          images: ['library1.jpg', 'library2.jpg']
        },
        season: 'all',
        status: 'active'
      },
      {
        id: 'project_003',
        title: '流浪动物救助站',
        subtitle: '照顾无家可归的小动物',
        description: '每一个小生命都值得被爱护，让我们为流浪的小动物建造温暖的家！',
        icon: '🐾',
        category: 'animal',
        targetAmount: 600,
        currentAmount: 0,
        donorCount: 0,
        energyCost: 8,
        rewardBadge: {
          name: '动物朋友',
          icon: '🐕',
          description: '成为流浪动物的守护天使'
        },
        story: {
          title: '小狗豆豆的故事',
          content: '豆豆是一只流浪的小狗，它最希望有一个温暖的家和好心人的关爱...',
          images: ['dog1.jpg', 'dog2.jpg']
        },
        season: 'summer',
        status: 'active'
      },
      {
        id: 'project_004',
        title: '地球清洁行动',
        subtitle: '清理海洋和森林垃圾',
        description: '保护我们美丽的地球家园，让蓝天白云永远陪伴我们！',
        icon: '🌍',
        category: 'environment',
        targetAmount: 1200,
        currentAmount: 0,
        donorCount: 0,
        energyCost: 15,
        rewardBadge: {
          name: '地球卫士',
          icon: '🌎',
          description: '为地球清洁做出贡献'
        },
        story: {
          title: '海龟妈妈的呼唤',
          content: '海龟妈妈说，海洋里有太多垃圾了，小鱼们都找不到干净的家...',
          images: ['ocean1.jpg', 'ocean2.jpg']
        },
        season: 'all',
        status: 'active'
      },
      {
        id: 'project_005',
        title: '温暖小屋计划',
        subtitle: '为困难家庭的孩子送温暖',
        description: '爱心可以传递温暖，让我们为需要帮助的小朋友送去关怀！',
        icon: '❤️',
        category: 'care',
        targetAmount: 500,
        currentAmount: 0,
        donorCount: 0,
        energyCost: 10,
        rewardBadge: {
          name: '温暖使者',
          icon: '🏠',
          description: '为困难家庭送去温暖'
        },
        story: {
          title: '小雨的新年愿望',
          content: '小雨的愿望很简单，就是希望家里能暖和一点，这样就不会冷了...',
          images: ['home1.jpg', 'home2.jpg']
        },
        season: 'winter',
        status: 'active'
      }
    ];
  }

  /**
   * 获取季节性主题
   */
  getSeasonalThemes() {
    return {
      spring: {
        name: '春日绿意',
        description: '万物复苏的季节，让我们为地球增添绿色',
        color: '#4CAF50',
        projects: ['project_001']
      },
      summer: {
        name: '夏日关爱',
        description: '炎热的夏天，让我们关爱需要帮助的小动物',
        color: '#FF9800',
        projects: ['project_003']
      },
      autumn: {
        name: '秋日感恩',
        description: '收获的季节，让我们分享知识和温暖',
        color: '#FF5722',
        projects: ['project_002', 'project_005']
      },
      winter: {
        name: '冬日温暖',
        description: '寒冷的冬天，让我们为他人送去温暖',
        color: '#E91E63',
        projects: ['project_005']
      }
    };
  }

  /**
   * 初始化慈善系统
   */
  initialize() {
    const charityData = wx.getStorageSync(this.storageKey);
    if (!charityData) {
      // 首次初始化
      const initialData = {
        projects: this.defaultProjects,
        userLevel: 1,
        totalDonations: 0,
        totalContribution: 0,
        joinDate: Date.now(),
        currentSeason: this.getCurrentSeason()
      };
      wx.setStorageSync(this.storageKey, initialData);
      return initialData;
    }
    return charityData;
  }

  /**
   * 获取当前季节
   */
  getCurrentSeason() {
    const month = new Date().getMonth() + 1;
    if (month >= 3 && month <= 5) return 'spring';
    if (month >= 6 && month <= 8) return 'summer';
    if (month >= 9 && month <= 11) return 'autumn';
    return 'winter';
  }

  /**
   * 获取所有慈善项目
   */
  getAllProjects() {
    const charityData = this.initialize();
    return charityData.projects || [];
  }

  /**
   * 获取当前季节的推荐项目
   */
  getSeasonalProjects() {
    const currentSeason = this.getCurrentSeason();
    const theme = this.seasonalThemes[currentSeason];
    const allProjects = this.getAllProjects();
    
    return {
      theme,
      projects: allProjects.filter(project => 
        theme.projects.includes(project.id) || project.season === 'all'
      )
    };
  }

  /**
   * 执行爱心能量捐赠
   */
  donate(projectId, energyAmount) {
    try {
      // 检查用户能量是否足够
      const userData = wx.getStorageSync('userData') || {};
      const currentLoveEnergy = userData.loveEnergy || 0;
      
      if (currentLoveEnergy < energyAmount) {
        return {
          success: false,
          message: '爱心能量不足，请先完成今日任务获取更多能量！'
        };
      }

      // 获取项目信息
      const charityData = this.initialize();
      const project = charityData.projects.find(p => p.id === projectId);
      
      if (!project) {
        return {
          success: false,
          message: '项目不存在'
        };
      }

      // 计算贡献点数
      const contributionPoints = Math.floor(energyAmount / project.energyCost);
      
      if (contributionPoints === 0) {
        return {
          success: false,
          message: `至少需要${project.energyCost}点爱心能量才能参与此项目`
        };
      }

      // 扣除用户能量
      userData.loveEnergy -= contributionPoints * project.energyCost;
      wx.setStorageSync('userData', userData);

      // 更新项目进度
      project.currentAmount += contributionPoints;
      project.donorCount += 1;

      // 更新用户慈善数据
      charityData.totalDonations += 1;
      charityData.totalContribution += contributionPoints;
      
      // 保存更新后的数据
      wx.setStorageSync(this.storageKey, charityData);

      // 记录捐赠历史
      this.recordDonation(projectId, contributionPoints, project.energyCost * contributionPoints);

      // 检查是否获得徽章
      const badge = this.checkBadgeEligibility(projectId, contributionPoints);

      return {
        success: true,
        message: `成功捐赠${contributionPoints}点贡献！`,
        contributionPoints,
        energyUsed: contributionPoints * project.energyCost,
        badge,
        projectProgress: {
          current: project.currentAmount,
          target: project.targetAmount,
          percentage: Math.min((project.currentAmount / project.targetAmount) * 100, 100)
        }
      };

    } catch (error) {
      console.error('捐赠失败:', error);
      return {
        success: false,
        message: '捐赠失败，请稍后重试'
      };
    }
  }

  /**
   * 记录捐赠历史
   */
  recordDonation(projectId, contributionPoints, energyUsed) {
    const donations = wx.getStorageSync(this.donationKey) || [];
    const newDonation = {
      id: `donation_${Date.now()}`,
      projectId,
      contributionPoints,
      energyUsed,
      timestamp: Date.now(),
      date: new Date().toLocaleDateString()
    };
    
    donations.unshift(newDonation); // 最新的在前面
    
    // 只保留最近100条记录
    if (donations.length > 100) {
      donations.splice(100);
    }
    
    wx.setStorageSync(this.donationKey, donations);
  }

  /**
   * 检查徽章获得资格
   */
  checkBadgeEligibility(projectId, contributionPoints) {
    const charityData = this.initialize();
    const project = charityData.projects.find(p => p.id === projectId);
    
    if (!project || !project.rewardBadge) return null;

    // 检查是否已经获得过这个徽章
    const existingBadges = wx.getStorageSync(this.badgeKey) || [];
    const hasThisBadge = existingBadges.some(badge => 
      badge.projectId === projectId && badge.type === 'first_donation'
    );

    if (!hasThisBadge) {
      // 颁发首次捐赠徽章
      const newBadge = {
        ...project.rewardBadge,
        projectId,
        type: 'first_donation',
        awardedAt: Date.now(),
        contributionPoints
      };
      
      existingBadges.push(newBadge);
      wx.setStorageSync(this.badgeKey, existingBadges);
      
      return newBadge;
    }

    return null;
  }

  /**
   * 获取用户慈善统计
   */
  getUserCharityStats() {
    const charityData = this.initialize();
    const donations = wx.getStorageSync(this.donationKey) || [];
    const badges = wx.getStorageSync(this.badgeKey) || [];

    // 计算爱心等级
    const loveLevel = this.calculateLoveLevel(charityData.totalContribution);

    return {
      totalDonations: charityData.totalDonations,
      totalContribution: charityData.totalContribution,
      loveLevel,
      badgeCount: badges.length,
      joinDate: charityData.joinDate,
      recentDonations: donations.slice(0, 5), // 最近5次捐赠
      favoriteCategory: this.getFavoriteCategory(donations)
    };
  }

  /**
   * 计算爱心等级
   */
  calculateLoveLevel(totalContribution) {
    if (totalContribution >= 300) return { level: 4, name: '爱心之星', icon: '⭐', next: null };
    if (totalContribution >= 150) return { level: 3, name: '爱心大树', icon: '🌳', next: 300 };
    if (totalContribution >= 50) return { level: 2, name: '爱心花朵', icon: '🌸', next: 150 };
    return { level: 1, name: '爱心萌芽', icon: '🌱', next: 50 };
  }

  /**
   * 获取最喜欢的慈善类别
   */
  getFavoriteCategory(donations) {
    if (donations.length === 0) return null;

    const categoryCount = {};
    const allProjects = this.getAllProjects();

    donations.forEach(donation => {
      const project = allProjects.find(p => p.id === donation.projectId);
      if (project) {
        categoryCount[project.category] = (categoryCount[project.category] || 0) + 1;
      }
    });

    const favoriteCategory = Object.keys(categoryCount).reduce((a, b) => 
      categoryCount[a] > categoryCount[b] ? a : b
    );

    const categoryNames = {
      environment: '环境保护',
      education: '教育支持',
      animal: '动物关爱',
      care: '温暖关怀'
    };

    return {
      category: favoriteCategory,
      name: categoryNames[favoriteCategory] || favoriteCategory,
      count: categoryCount[favoriteCategory]
    };
  }

  /**
   * 获取捐赠历史
   */
  getDonationHistory(limit = 20) {
    const donations = wx.getStorageSync(this.donationKey) || [];
    const allProjects = this.getAllProjects();

    return donations.slice(0, limit).map(donation => {
      const project = allProjects.find(p => p.id === donation.projectId);
      return {
        ...donation,
        projectTitle: project ? project.title : '未知项目',
        projectIcon: project ? project.icon : '❓'
      };
    });
  }
}

// 导出单例
const charitySystem = new CharitySystem();

module.exports = {
  charitySystem
};
