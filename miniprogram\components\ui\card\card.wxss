/* 能量星球卡片组件样式 */

.card {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  border: 2rpx solid rgba(255, 255, 255, 0.2);
  border-radius: 24rpx;
  padding: 32rpx;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.2);
  transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  box-sizing: border-box;
}

.card-hover:hover {
  transform: translateY(-4rpx);
  box-shadow: 0 16rpx 48rpx rgba(0, 0, 0, 0.3);
}

/* 卡片类型样式 */
.card-energy {
  border-color: rgba(255, 215, 106, 0.2);
  box-shadow: 0 8rpx 32rpx rgba(255, 215, 106, 0.4);
}

.card-wisdom {
  border-color: rgba(77, 159, 255, 0.2);
  box-shadow: 0 8rpx 32rpx rgba(77, 159, 255, 0.4);
}

.card-love {
  border-color: rgba(255, 107, 107, 0.2);
  box-shadow: 0 8rpx 32rpx rgba(255, 107, 107, 0.4);
}

.card-success {
  border-color: rgba(99, 226, 183, 0.2);
  box-shadow: 0 8rpx 32rpx rgba(99, 226, 183, 0.4);
}

/* 卡片头部 */
.card-header {
  padding-bottom: 24rpx;
  margin-bottom: 24rpx;
  border-bottom: 1rpx solid rgba(255, 255, 255, 0.2);
}

.card-title {
  font-size: 36rpx;
  font-weight: 600;
  color: #FFFFFF;
  line-height: 1.2;
}

/* 卡片内容 */
.card-body {
  flex: 1;
  color: #FFFFFF;
}

/* 卡片底部 */
.card-footer {
  padding-top: 24rpx;
  margin-top: 24rpx;
  border-top: 1rpx solid rgba(255, 255, 255, 0.2);
}
