# 《能量星球》微信小程序开发指南

## 📋 项目概述

### 产品定位
- **目标用户**：3-7岁儿童及其家庭
- **核心理念**：寓教于乐、寓善于乐的儿童全方位成长平台
- **主题框架**：宇宙探索 + 双能量系统（智慧能量🔷 + 爱心能量❤️）
- **设计哲学**：好奇心驱动、内在价值塑造、无挫败感设计、家长即伙伴

### 核心功能模块
1. **星际舰桥** - 儿童主界面（飞船驾驶舱）
2. **思维工坊** - 核心游戏区（主题星球探索）
3. **船长私人舱室** - 个人空间（成就展示、愿望合成）
4. **宇宙灯塔计划** - 慈善系统（虚拟与现实连接）
5. **地球指挥中心** - 家长后台（成长监控、亲子协作）

## 🎨 UI设计系统

### 设计原则优先级
1. **可访问性 > 视觉效果** - 确保所有儿童都能使用
2. **教育价值 > 娱乐效果** - 每个功能都要有明确的学习目标
3. **安全性 > 功能丰富性** - 儿童数据保护是第一要务
4. **简洁性 > 复杂性** - 避免认知过载

### 视觉风格：高对比度太空舱设计
- **核心概念**：太空舱控制面板，柔软但高对比度
- **形状语言**：圆润友好，发光边框，明亮色彩
- **对比度标准**：所有元素符合WCAG AA标准（≥4.5:1）

### 色彩系统
```css
/* 主色调 */
--space-deep: #1A183E;        /* 深空紫蓝 - 背景 */
--energy-yellow: #FFD76A;     /* 能量黄 - 行动/高亮 */
--wisdom-blue: #4D9FFF;       /* 智慧蓝 - 逻辑/益智 */
--love-red: #FF6B6B;          /* 爱心红 - 社交/慈善 */
--success-green: #63E2B7;     /* 成就绿 - 成功/反馈 */
--star-white: #FFFFFF;        /* 星光白 - 文本 */

/* 辅助色调 */
--space-gradient-start: #1A183E;
--space-gradient-end: #2D1B69;
--glow-effect: rgba(255, 215, 106, 0.3);
--shadow-soft: rgba(0, 0, 0, 0.2);
```

### 字体系统
```css
/* 字体大小 - 适配儿童阅读 */
--font-size-xl: 48rpx;        /* 主标题 */
--font-size-lg: 36rpx;        /* 副标题 */
--font-size-md: 32rpx;        /* 正文 */
--font-size-sm: 28rpx;        /* 辅助文字 */

/* 字体权重 */
--font-weight-bold: 700;
--font-weight-medium: 500;
--font-weight-normal: 400;
```

### 间距系统
```css
/* 间距单位 - 8rpx基准 */
--spacing-xs: 8rpx;
--spacing-sm: 16rpx;
--spacing-md: 24rpx;
--spacing-lg: 32rpx;
--spacing-xl: 48rpx;
--spacing-xxl: 64rpx;
```

### 圆角系统
```css
/* 圆角 - 友好柔和 */
--radius-sm: 8rpx;
--radius-md: 16rpx;
--radius-lg: 24rpx;
--radius-xl: 32rpx;
--radius-round: 50%;
```

## 🏗️ 技术架构

### 前端技术栈
- **框架**：微信小程序原生框架
- **样式**：WXSS + CSS变量系统
- **状态管理**：小程序原生 + 自定义状态管理
- **图形**：Canvas 2D + CSS动画（避免复杂3D）
- **音效**：微信小程序音频API

### 项目结构
```
miniprogram/
├── pages/                    # 页面目录
│   ├── index/               # 星际舰桥（主界面）
│   ├── workshop/            # 思维工坊
│   ├── quarters/            # 船长私人舱室
│   ├── beacon/              # 宇宙灯塔计划
│   └── parent/              # 地球指挥中心
├── components/              # 自定义组件
│   ├── ui/                  # 基础UI组件
│   ├── game/                # 游戏相关组件
│   └── layout/              # 布局组件
├── styles/                  # 样式文件
│   ├── variables.wxss       # CSS变量
│   ├── base.wxss           # 基础样式
│   └── components.wxss     # 组件样式
├── utils/                   # 工具函数
├── assets/                  # 静态资源
│   ├── images/             # 图片资源
│   ├── sounds/             # 音效资源
│   └── fonts/              # 字体资源
└── data/                   # 数据文件
```

## 📱 页面设计规范

### 通用布局原则
1. **安全区域**：顶部预留状态栏高度，底部预留Home指示器
2. **触摸目标**：最小44rpx×44rpx，推荐60rpx×60rpx
3. **内容边距**：页面左右边距32rpx，上下边距24rpx
4. **滚动区域**：确保内容可滚动，避免内容被遮挡

### 导航设计
- **主导航**：底部Tab导航，5个主要模块
- **页面导航**：顶部导航栏，返回按钮 + 页面标题
- **面包屑**：复杂页面提供路径指示

### 反馈系统
- **加载状态**：星空加载动画
- **成功反馈**：绿色✓图标 + 正向音效
- **错误提示**：温和的提示，避免负面情绪
- **操作反馈**：按钮点击动画，触觉反馈

## 🔧 开发阶段规划

### 第一阶段：基础UI框架（当前阶段）
- [x] 项目初始化和清理
- [ ] 全局样式系统
- [ ] 基础组件库
- [ ] 主界面框架
- [ ] 导航系统

### 第二阶段：核心功能实现
- [ ] 双能量系统
- [ ] 基础游戏模块
- [ ] 用户数据管理
- [ ] 家长看板基础版

### 第三阶段：高级功能
- [ ] 动态难度调整
- [ ] 社交功能
- [ ] 慈善系统
- [ ] 数据分析

## 📏 设计约束

### 性能约束
- **包大小**：单个分包不超过2MB
- **图片优化**：使用WebP格式，压缩率>70%
- **动画性能**：优先使用CSS动画，避免复杂JS动画
- **内存使用**：避免内存泄漏，及时清理资源

### 可访问性约束
- **对比度**：文字对比度≥4.5:1，图形对比度≥3:1
- **字体大小**：最小字体28rpx
- **触摸目标**：最小44rpx×44rpx
- **语音支持**：重要文字提供语音朗读

### 兼容性约束
- **微信版本**：支持微信7.0+
- **系统版本**：iOS 10+, Android 6.0+
- **屏幕适配**：支持iPhone SE到iPad的各种尺寸

## 🎯 开发规范

### 代码规范
- **命名**：使用语义化命名，组件采用PascalCase
- **注释**：关键逻辑必须添加注释
- **文件组织**：按功能模块组织，避免单文件过大

### 提交规范
```
feat: 新功能
fix: 修复bug
style: 样式调整
refactor: 重构
docs: 文档更新
test: 测试相关
```

## 📚 参考资源

### 设计参考
- [微信小程序设计指南](https://developers.weixin.qq.com/miniprogram/design/)
- [WCAG 2.1 可访问性指南](https://www.w3.org/WAI/WCAG21/quickref/)
- [儿童UI设计最佳实践](https://www.nngroup.com/articles/kids-websites/)

### 技术文档
- [微信小程序开发文档](https://developers.weixin.qq.com/miniprogram/dev/framework/)
- [小程序性能优化指南](https://developers.weixin.qq.com/miniprogram/dev/framework/performance/)

## 🧩 基础组件设计

### 按钮组件 (Button)
```css
/* 主要按钮 - 能量黄 */
.btn-primary {
  background: var(--energy-yellow);
  color: var(--space-deep);
  border-radius: var(--radius-lg);
  padding: var(--spacing-md) var(--spacing-lg);
  font-size: var(--font-size-md);
  font-weight: var(--font-weight-bold);
  box-shadow: 0 8rpx 16rpx var(--glow-effect);
  transition: all 0.3s ease;
}

/* 次要按钮 - 智慧蓝 */
.btn-secondary {
  background: var(--wisdom-blue);
  color: var(--star-white);
  /* 其他样式同primary */
}

/* 爱心按钮 - 爱心红 */
.btn-love {
  background: var(--love-red);
  color: var(--star-white);
  /* 其他样式同primary */
}
```

### 卡片组件 (Card)
```css
.card {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  border: 2rpx solid rgba(255, 255, 255, 0.2);
  border-radius: var(--radius-lg);
  padding: var(--spacing-lg);
  box-shadow: 0 8rpx 32rpx var(--shadow-soft);
}
```

### 能量条组件 (EnergyBar)
```css
.energy-bar {
  width: 100%;
  height: 16rpx;
  background: rgba(255, 255, 255, 0.2);
  border-radius: var(--radius-sm);
  overflow: hidden;
}

.energy-bar-fill {
  height: 100%;
  background: linear-gradient(90deg, var(--energy-yellow), var(--success-green));
  border-radius: var(--radius-sm);
  transition: width 0.5s ease;
}
```

### 图标系统
- **尺寸**：32rpx, 48rpx, 64rpx, 96rpx
- **风格**：线性图标，2rpx描边，圆润端点
- **颜色**：跟随父元素或使用主题色

## 🎮 交互设计规范

### 动画效果
```css
/* 按钮点击动画 */
@keyframes button-press {
  0% { transform: scale(1); }
  50% { transform: scale(0.95); }
  100% { transform: scale(1); }
}

/* 能量收集动画 */
@keyframes energy-collect {
  0% {
    transform: scale(1) rotate(0deg);
    opacity: 1;
  }
  100% {
    transform: scale(0.3) rotate(360deg);
    opacity: 0;
  }
}

/* 页面切换动画 */
@keyframes page-slide-in {
  0% {
    transform: translateX(100%);
    opacity: 0;
  }
  100% {
    transform: translateX(0);
    opacity: 1;
  }
}
```

### 音效设计
- **按钮点击**：轻柔的"滴"声（200ms）
- **能量收集**：上升音调的"叮"声（300ms）
- **成功完成**：愉悦的和弦（500ms）
- **页面切换**：轻微的"嗖"声（150ms）

## 📐 响应式设计

### 屏幕适配
```css
/* 小屏设备 (iPhone SE) */
@media (max-width: 375px) {
  .container {
    padding: var(--spacing-md);
  }

  .btn-primary {
    font-size: var(--font-size-sm);
    padding: var(--spacing-sm) var(--spacing-md);
  }
}

/* 大屏设备 (iPad) */
@media (min-width: 768px) {
  .container {
    max-width: 600px;
    margin: 0 auto;
  }
}
```

### 安全区域适配
```css
.page {
  padding-top: env(safe-area-inset-top);
  padding-bottom: env(safe-area-inset-bottom);
}
```

---

**下一步**：开始实现基础UI框架和组件库
