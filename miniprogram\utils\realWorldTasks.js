// 《能量星球》现实世界任务库
// 提供丰富的现实行为任务模板和个性化推荐

const realWorldTasks = {
  
  // 任务难度等级
  DIFFICULTY_LEVELS: {
    EASY: 1,      // 简单 (3-5岁适合)
    MEDIUM: 2,    // 中等 (6-9岁适合)
    HARD: 3       // 困难 (10-12岁适合)
  },

  // 季节性任务
  SEASONAL_TASKS: {
    spring: [
      {
        id: 'spring_001',
        name: '种植小植物',
        description: '种一颗种子，每天观察它的成长',
        category: 'social_participation',
        energyReward: { type: 'love', amount: 20 },
        difficulty: 2,
        duration: '长期任务'
      },
      {
        id: 'spring_002',
        name: '春游观察自然',
        description: '到公园观察春天的变化，记录看到的花草',
        category: 'social_participation',
        energyReward: { type: 'wisdom', amount: 15 },
        difficulty: 1,
        duration: '周末任务'
      }
    ],
    summer: [
      {
        id: 'summer_001',
        name: '户外运动锻炼',
        description: '每天进行30分钟户外活动',
        category: 'life_management',
        energyReward: { type: 'love', amount: 15 },
        difficulty: 2,
        duration: '每日任务'
      },
      {
        id: 'summer_002',
        name: '制作夏日饮品',
        description: '和家人一起制作健康的夏日饮品',
        category: 'family_care',
        energyReward: { type: 'love', amount: 18 },
        difficulty: 2,
        duration: '周末任务'
      }
    ],
    autumn: [
      {
        id: 'autumn_001',
        name: '收集秋天的叶子',
        description: '收集不同形状和颜色的落叶，制作标本',
        category: 'social_participation',
        energyReward: { type: 'wisdom', amount: 12 },
        difficulty: 1,
        duration: '周末任务'
      },
      {
        id: 'autumn_002',
        name: '感恩节活动',
        description: '写下今年最感谢的三件事',
        category: 'family_care',
        energyReward: { type: 'love', amount: 20 },
        difficulty: 2,
        duration: '特殊任务'
      }
    ],
    winter: [
      {
        id: 'winter_001',
        name: '温暖行动',
        description: '为需要帮助的人做一件温暖的事',
        category: 'social_participation',
        energyReward: { type: 'love', amount: 25 },
        difficulty: 3,
        duration: '周末任务'
      },
      {
        id: 'winter_002',
        name: '新年计划制定',
        description: '和家人一起制定新年的成长计划',
        category: 'family_care',
        energyReward: { type: 'wisdom', amount: 20 },
        difficulty: 2,
        duration: '特殊任务'
      }
    ]
  },

  // 节日特殊任务
  HOLIDAY_TASKS: {
    'new_year': [
      {
        id: 'ny_001',
        name: '新年祝福传递',
        description: '给长辈和朋友送上新年祝福',
        category: 'social_growth',
        energyReward: { type: 'love', amount: 15 }
      }
    ],
    'spring_festival': [
      {
        id: 'sf_001',
        name: '学习春节习俗',
        description: '了解春节的传统习俗和意义',
        category: 'social_participation',
        energyReward: { type: 'wisdom', amount: 18 }
      },
      {
        id: 'sf_002',
        name: '帮助准备年夜饭',
        description: '协助家人准备年夜饭',
        category: 'family_care',
        energyReward: { type: 'love', amount: 20 }
      }
    ],
    'childrens_day': [
      {
        id: 'cd_001',
        name: '分享快乐',
        description: '和其他小朋友分享自己的玩具或零食',
        category: 'social_growth',
        energyReward: { type: 'love', amount: 15 }
      }
    ],
    'teachers_day': [
      {
        id: 'td_001',
        name: '感谢老师',
        description: '制作感谢卡片或写感谢信给老师',
        category: 'social_growth',
        energyReward: { type: 'love', amount: 18 }
      }
    ]
  },

  // 年龄特定任务
  AGE_SPECIFIC_TASKS: {
    '3-5': [
      {
        id: 'age35_001',
        name: '学会系鞋带',
        description: '练习自己系鞋带',
        category: 'life_management',
        energyReward: { type: 'wisdom', amount: 12 },
        difficulty: 2
      },
      {
        id: 'age35_002',
        name: '认识颜色和形状',
        description: '在生活中找到不同颜色和形状的物品',
        category: 'life_management',
        energyReward: { type: 'wisdom', amount: 8 },
        difficulty: 1
      }
    ],
    '6-8': [
      {
        id: 'age68_001',
        name: '学会使用筷子',
        description: '练习正确使用筷子吃饭',
        category: 'life_management',
        energyReward: { type: 'wisdom', amount: 15 },
        difficulty: 2
      },
      {
        id: 'age68_002',
        name: '独立完成作业',
        description: '不需要家长督促，主动完成作业',
        category: 'life_management',
        energyReward: { type: 'wisdom', amount: 18 },
        difficulty: 3
      }
    ],
    '9-12': [
      {
        id: 'age912_001',
        name: '制定学习计划',
        description: '为自己制定一周的学习计划并执行',
        category: 'life_management',
        energyReward: { type: 'wisdom', amount: 25 },
        difficulty: 3
      },
      {
        id: 'age912_002',
        name: '参与社区服务',
        description: '参加社区的志愿服务活动',
        category: 'social_participation',
        energyReward: { type: 'love', amount: 30 },
        difficulty: 3
      }
    ]
  },

  // 兴趣导向任务
  INTEREST_BASED_TASKS: {
    'reading': [
      {
        id: 'read_001',
        name: '每日阅读时光',
        description: '每天阅读30分钟课外书',
        category: 'life_management',
        energyReward: { type: 'wisdom', amount: 12 }
      },
      {
        id: 'read_002',
        name: '故事分享会',
        description: '和家人分享今天读到的有趣故事',
        category: 'family_care',
        energyReward: { type: 'love', amount: 15 }
      }
    ],
    'sports': [
      {
        id: 'sport_001',
        name: '运动小达人',
        description: '每天进行一项体育运动',
        category: 'life_management',
        energyReward: { type: 'love', amount: 15 }
      },
      {
        id: 'sport_002',
        name: '运动伙伴',
        description: '邀请朋友一起运动',
        category: 'social_growth',
        energyReward: { type: 'love', amount: 18 }
      }
    ],
    'art': [
      {
        id: 'art_001',
        name: '创意手工制作',
        description: '用废旧材料制作一件艺术品',
        category: 'social_participation',
        energyReward: { type: 'wisdom', amount: 20 }
      },
      {
        id: 'art_002',
        name: '艺术作品分享',
        description: '向家人展示自己的艺术作品',
        category: 'family_care',
        energyReward: { type: 'love', amount: 12 }
      }
    ],
    'music': [
      {
        id: 'music_001',
        name: '音乐欣赏时间',
        description: '每天听一首经典音乐并分享感受',
        category: 'life_management',
        energyReward: { type: 'wisdom', amount: 10 }
      },
      {
        id: 'music_002',
        name: '家庭音乐会',
        description: '为家人表演一首歌曲或乐器',
        category: 'family_care',
        energyReward: { type: 'love', amount: 18 }
      }
    ]
  },

  // 获取当前季节
  getCurrentSeason() {
    const month = new Date().getMonth() + 1;
    if (month >= 3 && month <= 5) return 'spring';
    if (month >= 6 && month <= 8) return 'summer';
    if (month >= 9 && month <= 11) return 'autumn';
    return 'winter';
  },

  // 获取年龄组
  getAgeGroup(age) {
    if (age >= 3 && age <= 5) return '3-5';
    if (age >= 6 && age <= 8) return '6-8';
    if (age >= 9 && age <= 12) return '9-12';
    return '6-8'; // 默认
  },

  // 根据用户特征推荐任务
  getRecommendedTasks(userData = {}) {
    const age = userData.age || 8;
    const interests = userData.interests || [];
    const currentSeason = this.getCurrentSeason();
    const ageGroup = this.getAgeGroup(age);
    
    let recommendedTasks = [];

    // 添加季节性任务
    const seasonalTasks = this.SEASONAL_TASKS[currentSeason] || [];
    recommendedTasks = recommendedTasks.concat(seasonalTasks.slice(0, 2));

    // 添加年龄特定任务
    const ageSpecificTasks = this.AGE_SPECIFIC_TASKS[ageGroup] || [];
    recommendedTasks = recommendedTasks.concat(ageSpecificTasks.slice(0, 2));

    // 根据兴趣添加任务
    interests.forEach(interest => {
      const interestTasks = this.INTEREST_BASED_TASKS[interest] || [];
      recommendedTasks = recommendedTasks.concat(interestTasks.slice(0, 1));
    });

    // 检查是否有节日任务
    const holidayTasks = this.getHolidayTasks();
    if (holidayTasks.length > 0) {
      recommendedTasks = recommendedTasks.concat(holidayTasks.slice(0, 1));
    }

    // 去重并限制数量
    const uniqueTasks = this.removeDuplicateTasks(recommendedTasks);
    return uniqueTasks.slice(0, 8); // 最多返回8个推荐任务
  },

  // 获取节日任务
  getHolidayTasks() {
    const today = new Date();
    const month = today.getMonth() + 1;
    const date = today.getDate();
    
    // 简单的节日判断逻辑
    if (month === 1 && date === 1) {
      return this.HOLIDAY_TASKS['new_year'] || [];
    }
    if (month === 6 && date === 1) {
      return this.HOLIDAY_TASKS['childrens_day'] || [];
    }
    if (month === 9 && date === 10) {
      return this.HOLIDAY_TASKS['teachers_day'] || [];
    }
    
    return [];
  },

  // 去除重复任务
  removeDuplicateTasks(tasks) {
    const seen = new Set();
    return tasks.filter(task => {
      if (seen.has(task.id)) {
        return false;
      }
      seen.add(task.id);
      return true;
    });
  },

  // 根据完成历史调整任务推荐
  adjustTasksBasedOnHistory(tasks, completionHistory = {}) {
    return tasks.map(task => {
      const taskHistory = completionHistory[task.id];
      if (taskHistory) {
        // 如果任务经常完成，可以增加难度或奖励
        if (taskHistory.completionRate > 0.8) {
          task.energyReward.amount = Math.round(task.energyReward.amount * 1.2);
          task.description += ' (挑战升级)';
        }
        // 如果任务经常失败，可以降低难度
        else if (taskHistory.completionRate < 0.3) {
          task.difficulty = Math.max(1, task.difficulty - 1);
          task.description += ' (简化版本)';
        }
      }
      return task;
    });
  },

  // 获取任务完成建议
  getTaskCompletionTips(taskId) {
    const tips = {
      // 生活管理类提示
      'life_001': '可以在洗手间贴一个提醒贴纸，帮助记住刷牙',
      'life_002': '从整理一个小角落开始，比如书桌或玩具箱',
      'life_003': '可以请家长帮忙设置一个好听的闹钟铃声',
      
      // 家庭关爱类提示
      'family_001': '选择力所能及的家务，比如摆放餐具或收拾玩具',
      'family_002': '可以用拥抱、画画或写小纸条的方式表达感谢',
      'family_003': '分享时可以说说今天最有趣或最开心的事情',
      
      // 社交成长类提示
      'social_001': '微笑着说"你好"会让问候更加温暖',
      'social_002': '记住"请"、"谢谢"、"对不起"这三个魔法词语',
      'social_003': '观察身边需要帮助的同学，主动伸出援手',
      
      // 社会参与类提示
      'participation_001': '学会区分可回收、有害、厨余和其他垃圾',
      'participation_002': '离开房间时记得关灯，刷牙时记得关水龙头',
      'participation_003': '爱护公共设施就像爱护自己的家一样'
    };

    return tips[taskId] || '坚持每天练习，你一定可以做到的！';
  }
};

module.exports = {
  realWorldTasks
};
