// 《能量星球》学习报告生成系统

/**
 * 学习报告生成器
 */
const ReportGenerator = {
  storageKey: 'learningReports',
  statsKey: 'dailyStats',

  /**
   * 生成周度学习报告
   */
  generateWeeklyReport(childData, aiReport) {
    const weeklyStats = this.calculateWeeklyStats();
    const abilityAnalysis = this.analyzeAbilityProgress(aiReport);
    const gameAnalysis = this.generateMockGameAnalysis();
    const recommendations = this.generateRecommendations(abilityAnalysis);
    
    const report = {
      id: `weekly_${Date.now()}`,
      type: 'weekly',
      period: this.getWeekPeriod(),
      generatedAt: Date.now(),
      childData: {
        name: childData.captainName,
        level: childData.level,
        currentEnergy: {
          wisdom: childData.wisdomEnergy,
          love: childData.loveEnergy
        }
      },
      summary: {
        totalStudyTime: weeklyStats.totalTime,
        averageDailyTime: weeklyStats.averageDaily,
        gamesCompleted: weeklyStats.gamesCompleted,
        energyGained: weeklyStats.energyGained,
        improvementRate: weeklyStats.improvementRate
      },
      abilityProgress: abilityAnalysis,
      gamePerformance: gameAnalysis,
      achievements: this.getWeeklyAchievements(),
      recommendations: recommendations,
      chartData: this.generateChartData(weeklyStats)
    };

    this.saveReport(report);
    return report;
  },

  /**
   * 计算周度统计数据
   */
  calculateWeeklyStats() {
    const today = new Date();
    const weekAgo = new Date(today.getTime() - 7 * 24 * 60 * 60 * 1000);
    
    let totalTime = 0;
    let gamesCompleted = 0;
    let energyGained = { wisdom: 0, love: 0 };
    const dailyData = [];

    // 模拟一周的数据
    for (let i = 0; i < 7; i++) {
      const date = new Date(weekAgo.getTime() + i * 24 * 60 * 60 * 1000);
      const dateStr = date.toDateString();
      
      // 从本地存储获取或生成模拟数据
      const dayStats = wx.getStorageSync(`stats_${dateStr}`) || this.generateMockDayStats();
      
      totalTime += dayStats.studyTime || 0;
      gamesCompleted += dayStats.gamesCompleted || 0;
      energyGained.wisdom += dayStats.wisdomGained || 0;
      energyGained.love += dayStats.loveGained || 0;
      
      dailyData.push({
        date: dateStr,
        studyTime: dayStats.studyTime || 0,
        games: dayStats.gamesCompleted || 0
      });
    }

    return {
      totalTime,
      averageDaily: Math.round(totalTime / 7),
      gamesCompleted,
      energyGained,
      improvementRate: this.calculateImprovementRate(dailyData),
      dailyData
    };
  },

  /**
   * 生成模拟日统计数据
   */
  generateMockDayStats() {
    return {
      studyTime: Math.floor(Math.random() * 60) + 20, // 20-80分钟
      gamesCompleted: Math.floor(Math.random() * 5) + 1, // 1-5个游戏
      wisdomGained: Math.floor(Math.random() * 20) + 10, // 10-30点
      loveGained: Math.floor(Math.random() * 15) + 5 // 5-20点
    };
  },

  /**
   * 分析能力进步
   */
  analyzeAbilityProgress(aiReport) {
    if (!aiReport || !aiReport.abilities) {
      return this.generateMockAbilityProgress();
    }

    const progress = {};
    Object.entries(aiReport.abilities).forEach(([ability, data]) => {
      const previous = Math.max(0, data.score - Math.floor(Math.random() * 10) + 5);
      progress[ability] = {
        current: data.score,
        previous: previous,
        trend: data.trend,
        improvement: Math.round(((data.score - previous) / previous) * 100)
      };
    });

    return progress;
  },

  /**
   * 生成模拟能力进步数据
   */
  generateMockAbilityProgress() {
    const abilities = ['logic', 'creativity', 'memory', 'empathy', 'problemSolving', 'attention'];
    const progress = {};
    
    abilities.forEach(ability => {
      const current = 60 + Math.floor(Math.random() * 30);
      const previous = Math.max(0, current - Math.floor(Math.random() * 15) + 5);
      progress[ability] = {
        current,
        previous,
        trend: current > previous ? 'up' : current < previous ? 'down' : 'stable',
        improvement: Math.round(((current - previous) / previous) * 100)
      };
    });
    
    return progress;
  },

  /**
   * 生成模拟游戏分析数据
   */
  generateMockGameAnalysis() {
    return {
      totalGames: 25,
      averageAccuracy: 78,
      favoriteGameType: 'logic',
      improvementAreas: ['creativity', 'memory'],
      strengths: ['logic', 'attention'],
      weeklyProgress: [65, 70, 75, 78, 80, 82, 85]
    };
  },

  /**
   * 生成模拟能力趋势数据
   */
  generateMockAbilityTrends() {
    const abilities = ['logic', 'creativity', 'memory', 'empathy', 'problemSolving', 'attention'];
    const trends = {};
    
    abilities.forEach(ability => {
      const current = 60 + Math.floor(Math.random() * 30);
      const previous = Math.max(0, current - Math.floor(Math.random() * 15) + 5);
      trends[ability] = {
        current,
        previous,
        trend: current > previous ? 'up' : current < previous ? 'down' : 'stable',
        improvement: Math.round(((current - previous) / previous) * 100),
        monthlyData: Array.from({ length: 4 }, () => Math.floor(Math.random() * 30) + 60)
      };
    });
    
    return trends;
  },

  /**
   * 生成图表数据
   */
  generateChartData(stats) {
    if (stats.dailyData) {
      return stats.dailyData.map(day => Math.min(100, (day.studyTime / 60) * 100));
    }
    
    // 生成模拟图表数据
    return Array.from({ length: 7 }, () => Math.floor(Math.random() * 80) + 20);
  },

  /**
   * 获取周期字符串
   */
  getWeekPeriod() {
    const today = new Date();
    const weekAgo = new Date(today.getTime() - 7 * 24 * 60 * 60 * 1000);
    return `${weekAgo.toLocaleDateString()} - ${today.toLocaleDateString()}`;
  },

  /**
   * 计算改进率
   */
  calculateImprovementRate(dailyData) {
    if (dailyData.length < 2) return 0;
    
    const firstHalf = dailyData.slice(0, Math.floor(dailyData.length / 2));
    const secondHalf = dailyData.slice(Math.floor(dailyData.length / 2));
    
    const firstAvg = firstHalf.reduce((sum, day) => sum + day.studyTime, 0) / firstHalf.length;
    const secondAvg = secondHalf.reduce((sum, day) => sum + day.studyTime, 0) / secondHalf.length;
    
    return Math.round(((secondAvg - firstAvg) / firstAvg) * 100);
  },

  /**
   * 生成建议
   */
  generateRecommendations(abilityProgress) {
    const recommendations = [];
    
    Object.entries(abilityProgress).forEach(([ability, data]) => {
      if (data.improvement < 0) {
        recommendations.push({
          type: 'improvement',
          ability,
          suggestion: this.getImprovementSuggestion(ability),
          priority: 'high'
        });
      } else if (data.improvement > 20) {
        recommendations.push({
          type: 'praise',
          ability,
          suggestion: `${ability}能力进步显著，继续保持！`,
          priority: 'low'
        });
      }
    });
    
    return recommendations.slice(0, 3);
  },

  /**
   * 获取改进建议
   */
  getImprovementSuggestion(ability) {
    const suggestions = {
      logic: '建议增加逻辑推理类游戏时间',
      creativity: '可以尝试更多创意类活动',
      memory: '推荐记忆训练游戏',
      empathy: '建议参与更多合作类活动',
      problemSolving: '增加解谜类游戏练习',
      attention: '建议设置专注时间，减少干扰'
    };
    
    return suggestions[ability] || '建议多样化学习内容';
  },

  /**
   * 获取周度成就
   */
  getWeeklyAchievements() {
    return [
      { name: '连续学习7天', icon: '🏆', achieved: true },
      { name: '完成10个游戏', icon: '🎮', achieved: true },
      { name: '获得100智慧能量', icon: '🔷', achieved: false }
    ];
  },

  /**
   * 保存报告
   */
  saveReport(report) {
    const reports = wx.getStorageSync(this.storageKey) || [];
    reports.unshift(report);
    
    if (reports.length > 10) {
      reports.splice(10);
    }
    
    wx.setStorageSync(this.storageKey, reports);
  },

  /**
   * 获取最新报告
   */
  getLatestReport(type = 'weekly') {
    const reports = wx.getStorageSync(this.storageKey) || [];
    return reports.find(r => r.type === type) || null;
  }
};

// 导出单例
const reportGenerator = ReportGenerator;

module.exports = {
  reportGenerator
};
