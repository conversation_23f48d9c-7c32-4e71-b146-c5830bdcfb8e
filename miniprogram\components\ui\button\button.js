// 能量星球按钮组件
Component({
  properties: {
    // 按钮类型：primary, secondary, love, success, ghost
    type: {
      type: String,
      value: 'primary'
    },
    // 按钮尺寸：sm, md, lg
    size: {
      type: String,
      value: 'md'
    },
    // 按钮文字
    text: {
      type: String,
      value: ''
    },
    // 图标路径
    icon: {
      type: String,
      value: ''
    },
    // 图标尺寸
    iconSize: {
      type: String,
      value: 'sm'
    },
    // 是否圆形
    round: {
      type: Boolean,
      value: false
    },
    // 是否禁用
    disabled: {
      type: Boolean,
      value: false
    },
    // 是否加载中
    loading: {
      type: Boolean,
      value: false
    }
  },

  data: {
    // 组件内部数据
  },

  methods: {
    // 点击事件
    onTap(e) {
      if (this.data.disabled || this.data.loading) {
        return;
      }
      
      // 触发父组件事件
      this.triggerEvent('tap', {
        type: this.data.type,
        text: this.data.text
      });
      
      // 播放点击音效（如果需要）
      this.playClickSound();
    },

    // 播放点击音效
    playClickSound() {
      // TODO: 实现音效播放
      console.log('播放按钮点击音效');
    }
  },

  lifetimes: {
    attached() {
      // 组件初始化
      console.log('按钮组件初始化');
    }
  }
});
