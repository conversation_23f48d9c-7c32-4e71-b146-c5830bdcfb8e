// 《能量星球》宇宙灯塔 - 慈善系统主逻辑
const { charitySystem } = require('../../utils/charitySystem');

Page({
  data: {
    // 用户状态
    currentLoveEnergy: 0,
    userStats: {
      totalDonations: 0,
      totalContribution: 0,
      loveLevel: { level: 1, name: '爱心萌芽', icon: '🌱', next: 50 },
      badgeCount: 0,
      favoriteCategory: null
    },
    loveLevelProgress: 0,

    // 季节主题
    seasonalTheme: null,

    // 项目数据
    activeProjects: [],
    recommendedProject: null,

    // 最近活动
    recentDonations: [],

    // 爱心传递链
    showLoveChain: false,

    // 页面状态
    loading: false,
    celebrating: false,
    celebrationMessage: ''
  },

  onLoad: function (options) {
    console.log('宇宙灯塔加载');
    this.initializeCharity();
  },

  onReady: function () {
    console.log('宇宙灯塔渲染完成');
  },

  onShow: function () {
    // 每次显示时刷新数据
    this.refreshCharityData();
  },

  onPullDownRefresh: function () {
    // 下拉刷新
    this.refreshCharityData();
    setTimeout(() => {
      wx.stopPullDownRefresh();
    }, 1500);
  },

  // 初始化慈善系统
  initializeCharity() {
    this.setData({ loading: true });

    try {
      // 初始化慈善系统
      charitySystem.initialize();

      // 加载用户数据
      this.loadUserData();

      // 加载季节主题
      this.loadSeasonalTheme();

      // 加载项目数据
      this.loadProjectsData();

      // 加载用户统计
      this.loadUserStats();

      // 加载最近活动
      this.loadRecentActivities();

      // 检查爱心传递链
      this.checkLoveChain();

    } catch (error) {
      console.error('初始化慈善系统失败:', error);
      wx.showToast({
        title: '加载失败，请重试',
        icon: 'none'
      });
    } finally {
      this.setData({ loading: false });
    }
  },

  // 加载用户数据
  loadUserData() {
    const userData = wx.getStorageSync('userData') || {};
    this.setData({
      currentLoveEnergy: userData.loveEnergy || 0
    });
  },

  // 加载季节主题
  loadSeasonalTheme() {
    const seasonalData = charitySystem.getSeasonalProjects();
    this.setData({
      seasonalTheme: seasonalData.theme
    });
  },

  // 加载项目数据
  loadProjectsData() {
    const allProjects = charitySystem.getAllProjects();
    const activeProjects = allProjects.filter(project => project.status === 'active');
    
    // 计算项目进度百分比
    const projectsWithProgress = activeProjects.map(project => ({
      ...project,
      progressPercentage: Math.min((project.currentAmount / project.targetAmount) * 100, 100)
    }));

    // 选择推荐项目（当前季节的第一个项目）
    const seasonalData = charitySystem.getSeasonalProjects();
    const recommendedProject = seasonalData.projects.length > 0 ? 
      projectsWithProgress.find(p => p.id === seasonalData.projects[0].id) || projectsWithProgress[0] : 
      projectsWithProgress[0];

    this.setData({
      activeProjects: projectsWithProgress,
      recommendedProject
    });
  },

  // 加载用户统计
  loadUserStats() {
    const userStats = charitySystem.getUserCharityStats();
    
    // 计算爱心等级进度
    let loveLevelProgress = 0;
    if (userStats.loveLevel.next) {
      loveLevelProgress = (userStats.totalContribution / userStats.loveLevel.next) * 100;
    }

    this.setData({
      userStats,
      loveLevelProgress
    });
  },

  // 加载最近活动
  loadRecentActivities() {
    const recentDonations = charitySystem.getDonationHistory(5);
    this.setData({
      recentDonations
    });
  },

  // 检查爱心传递链
  checkLoveChain() {
    const userStats = charitySystem.getUserCharityStats();
    // 如果用户有捐赠记录，显示爱心传递链
    this.setData({
      showLoveChain: userStats.totalDonations > 0
    });
  },

  // 刷新慈善数据
  refreshCharityData() {
    this.setData({ loading: true });
    
    setTimeout(() => {
      this.loadUserData();
      this.loadProjectsData();
      this.loadUserStats();
      this.loadRecentActivities();
      this.setData({ loading: false });
    }, 1000);
  },

  // 打开爱心项目中心
  onOpenProjectCenter() {
    console.log('打开爱心项目中心');

    // 显示项目列表弹窗
    const projects = this.data.activeProjects;
    const projectTitles = projects.map(p => `${p.icon} ${p.title} (${p.progressPercentage.toFixed(0)}%)`);

    wx.showActionSheet({
      itemList: projectTitles,
      success: (res) => {
        const selectedProject = projects[res.tapIndex];
        this.showProjectDetail(selectedProject);
      }
    });
  },

  // 显示项目详情
  showProjectDetail(project) {
    const progressText = `进度：${project.currentAmount}/${project.targetAmount} (${project.progressPercentage.toFixed(1)}%)`;
    const costText = `捐赠消耗：${project.energyCost}点爱心能量`;
    const content = `${project.description}\n\n${progressText}\n${costText}`;

    wx.showModal({
      title: `${project.icon} ${project.title}`,
      content: content,
      confirmText: '立即捐赠',
      cancelText: '查看故事',
      success: (res) => {
        if (res.confirm) {
          // 立即捐赠
          this.performDonation(project.id, project.energyCost);
        } else if (res.cancel) {
          // 查看故事
          this.showProjectStory(project);
        }
      }
    });
  },

  // 显示项目故事
  showProjectStory(project) {
    if (project.story) {
      wx.showModal({
        title: project.story.title,
        content: project.story.content,
        showCancel: false,
        confirmText: '好感人！'
      });
    }
  },

  // 打开能量捐赠站
  onOpenDonationStation() {
    console.log('打开能量捐赠站');

    const currentEnergy = this.data.currentLoveEnergy;
    if (currentEnergy < 8) {
      wx.showModal({
        title: '爱心能量不足',
        content: '你的爱心能量不足，请先完成今日任务获取更多能量！\n\n当前能量：' + currentEnergy + '点\n最低捐赠：8点',
        showCancel: false,
        confirmText: '去完成任务',
        success: () => {
          wx.navigateTo({
            url: '/pages/dailyTasks/index'
          });
        }
      });
      return;
    }

    // 显示捐赠选项
    const donationOptions = [
      '捐赠 8点 ❤️ (1个贡献点)',
      '捐赠 16点 ❤️ (2个贡献点)',
      '捐赠 24点 ❤️ (3个贡献点)',
      '捐赠 32点 ❤️ (4个贡献点)',
      '自定义捐赠金额'
    ];

    wx.showActionSheet({
      itemList: donationOptions,
      success: (res) => {
        let donationAmount;
        switch(res.tapIndex) {
          case 0: donationAmount = 8; break;
          case 1: donationAmount = 16; break;
          case 2: donationAmount = 24; break;
          case 3: donationAmount = 32; break;
          case 4:
            this.showCustomDonationInput();
            return;
        }

        if (donationAmount <= currentEnergy) {
          this.selectProjectForDonation(donationAmount);
        } else {
          wx.showToast({
            title: '能量不足',
            icon: 'none'
          });
        }
      }
    });
  },

  // 显示自定义捐赠输入
  showCustomDonationInput() {
    wx.showModal({
      title: '自定义捐赠',
      content: `请输入捐赠的爱心能量数量\n(当前可用：${this.data.currentLoveEnergy}点)`,
      editable: true,
      placeholderText: '请输入数字',
      success: (res) => {
        if (res.confirm && res.content) {
          const amount = parseInt(res.content);
          if (isNaN(amount) || amount < 8) {
            wx.showToast({
              title: '最少捐赠8点能量',
              icon: 'none'
            });
          } else if (amount > this.data.currentLoveEnergy) {
            wx.showToast({
              title: '能量不足',
              icon: 'none'
            });
          } else {
            this.selectProjectForDonation(amount);
          }
        }
      }
    });
  },

  // 选择捐赠项目
  selectProjectForDonation(energyAmount) {
    const projects = this.data.activeProjects;
    const projectTitles = projects.map(p => `${p.icon} ${p.title}`);

    wx.showActionSheet({
      itemList: projectTitles,
      success: (res) => {
        const selectedProject = projects[res.tapIndex];
        this.confirmDonation(selectedProject.id, energyAmount);
      }
    });
  },

  // 确认捐赠
  confirmDonation(projectId, energyAmount) {
    const project = this.data.activeProjects.find(p => p.id === projectId);
    const contributionPoints = Math.floor(energyAmount / project.energyCost);

    wx.showModal({
      title: '确认捐赠',
      content: `向"${project.title}"捐赠${energyAmount}点爱心能量\n将获得${contributionPoints}个贡献点\n\n确定要捐赠吗？`,
      confirmText: '确定捐赠',
      cancelText: '取消',
      success: (res) => {
        if (res.confirm) {
          this.performDonation(projectId, energyAmount);
        }
      }
    });
  },

  // 打开善意行为指南
  onOpenKindnessGuide() {
    console.log('打开善意行为指南');
    wx.showToast({
      title: '善意指南开发中...',
      icon: 'none'
    });
  },

  // 打开爱心成长档案
  onOpenGrowthArchive() {
    console.log('打开爱心成长档案');

    const stats = this.data.userStats;
    const joinDate = new Date(stats.joinDate || Date.now()).toLocaleDateString();

    let archiveContent = `🌱 爱心等级：${stats.loveLevel.name}\n`;
    archiveContent += `💝 总捐赠次数：${stats.totalDonations}次\n`;
    archiveContent += `⭐ 总贡献点数：${stats.totalContribution}点\n`;
    archiveContent += `🏆 获得徽章：${stats.badgeCount}个\n`;
    archiveContent += `📅 加入日期：${joinDate}\n`;

    if (stats.favoriteCategory) {
      archiveContent += `❤️ 最喜欢：${stats.favoriteCategory.name}\n`;
    }

    // 显示等级进度
    if (stats.loveLevel.next) {
      const remaining = stats.loveLevel.next - stats.totalContribution;
      archiveContent += `\n🎯 距离下一级还需：${remaining}点贡献`;
    } else {
      archiveContent += `\n🌟 已达到最高等级！`;
    }

    wx.showModal({
      title: `${stats.loveLevel.icon} 我的爱心档案`,
      content: archiveContent,
      confirmText: '查看徽章',
      cancelText: '查看历史',
      success: (res) => {
        if (res.confirm) {
          this.showBadges();
        } else if (res.cancel) {
          this.showDonationHistory();
        }
      }
    });
  },

  // 显示徽章
  showBadges() {
    const badges = wx.getStorageSync('charityBadges') || [];

    if (badges.length === 0) {
      wx.showModal({
        title: '暂无徽章',
        content: '你还没有获得任何慈善徽章，快去参与慈善项目吧！',
        showCancel: false
      });
      return;
    }

    const badgeList = badges.map(badge => `${badge.icon} ${badge.name}`);

    wx.showActionSheet({
      itemList: badgeList,
      success: (res) => {
        const selectedBadge = badges[res.tapIndex];
        wx.showModal({
          title: `${selectedBadge.icon} ${selectedBadge.name}`,
          content: selectedBadge.description + '\n\n获得时间：' + new Date(selectedBadge.awardedAt).toLocaleDateString(),
          showCancel: false
        });
      }
    });
  },

  // 显示捐赠历史
  showDonationHistory() {
    const history = this.data.recentDonations;

    if (history.length === 0) {
      wx.showModal({
        title: '暂无记录',
        content: '你还没有任何捐赠记录，快去参与慈善项目吧！',
        showCancel: false
      });
      return;
    }

    const historyList = history.map(item =>
      `${item.projectIcon} ${item.projectTitle} - ${item.contributionPoints}点`
    );

    wx.showActionSheet({
      itemList: historyList,
      success: (res) => {
        const selectedRecord = history[res.tapIndex];
        wx.showModal({
          title: '捐赠详情',
          content: `项目：${selectedRecord.projectTitle}\n贡献：${selectedRecord.contributionPoints}点\n消耗：${selectedRecord.energyUsed}点爱心能量\n时间：${selectedRecord.date}`,
          showCancel: false
        });
      }
    });
  },

  // 打开温暖故事馆
  onOpenStoryHall() {
    console.log('打开温暖故事馆');
    wx.showToast({
      title: '故事馆开发中...',
      icon: 'none'
    });
  },

  // 打开家庭善意计划
  onOpenFamilyPlan() {
    console.log('打开家庭善意计划');
    wx.showToast({
      title: '家庭计划开发中...',
      icon: 'none'
    });
  },

  // 快速捐赠
  onQuickDonate(e) {
    const project = e.currentTarget.dataset.project;
    if (!project) return;

    console.log('快速捐赠:', project.title);
    this.performDonation(project.id, project.energyCost);
  },

  // 执行捐赠
  performDonation(projectId, energyAmount) {
    const result = charitySystem.donate(projectId, energyAmount);
    
    if (result.success) {
      // 捐赠成功
      this.triggerCelebration(result.message);
      
      // 刷新数据
      this.refreshCharityData();
      
      // 如果获得了徽章，显示特殊庆祝
      if (result.badge) {
        setTimeout(() => {
          this.triggerCelebration(`🎉 获得新徽章：${result.badge.name}！`);
        }, 2000);
      }
      
    } else {
      // 捐赠失败
      wx.showToast({
        title: result.message,
        icon: 'none',
        duration: 2000
      });
    }
  },

  // 触发庆祝动画
  triggerCelebration(message) {
    this.setData({
      celebrating: true,
      celebrationMessage: message
    });

    setTimeout(() => {
      this.setData({
        celebrating: false,
        celebrationMessage: ''
      });
    }, 3000);
  }
});
