# 《能量星球》故障排除指南

## 🔧 编译错误解决方案

### 问题1：云函数环境未选择
**症状**：编译时提示"未选择环境"或云函数相关错误

**解决方案**：
1. 我们已经移除了云函数配置，因为当前阶段不需要云开发
2. 如果仍有问题，请检查以下文件：

#### 检查 `project.config.json`
确保文件中没有以下配置：
```json
"cloudfunctionRoot": "cloudfunctions/",
"cloudfunctionTemplateRoot": "cloudfunctionTemplate/",
```

#### 检查项目目录
确保没有空的 `cloudfunctions` 目录

### 问题2：JSON文件编码错误
**症状**：`SyntaxError: Unexpected token ﻿ in JSON at position 0`

**原因**：JSON文件包含BOM（Byte Order Mark）字符

**解决方案**：
1. 删除有问题的JSON文件：`Remove-Item "文件路径" -Force`
2. 用PowerShell重新创建无BOM的文件：
```powershell
$bytes = [System.Text.Encoding]::UTF8.GetBytes('JSON内容');
[System.IO.File]::WriteAllBytes("文件路径", $bytes)
```
3. ✅ 已修复：`miniprogram/pages/index/index.json`

### 问题3：组件引用错误
**症状**：页面显示组件未找到

**解决方案**：
检查 `miniprogram/pages/index/index.json` 中的组件引用：
```json
{
  "usingComponents": {
    "ui-button": "/components/ui/button/button",
    "ui-card": "/components/ui/card/card"
  }
}
```

### 问题3：WXSS语法错误
**症状**：`unexpected token` 或样式编译失败

**原因**：微信小程序WXSS不支持某些CSS语法

**已修复的问题**：
- 移除了通用选择器 `*`
- 移除了 `cursor` 和 `user-select` 属性
- 移除了 `-webkit-line-clamp` 等不支持的属性
- 简化了样式导入结构

### 问题4：样式不生效
**症状**：页面显示为白色背景或样式异常

**解决方案**：
1. 检查 `miniprogram/app.wxss` 是否正确引入了样式文件
2. 确保样式文件语法符合WXSS规范
3. 避免使用微信小程序不支持的CSS属性

## 🚀 编译步骤

1. **清理缓存**：在微信开发者工具中点击"清缓存" -> "清除全部缓存"
2. **重新编译**：点击"编译"按钮
3. **检查控制台**：查看是否有错误信息

## 📱 预览效果

编译成功后，您应该看到：
- 深空紫蓝色背景
- 顶部能量显示条
- 三个功能卡片（思维工坊、船长舱室、宇宙灯塔）
- 底部快捷操作按钮

## 🔍 常见问题

### Q: 为什么删除了云函数目录？
A: 当前阶段我们专注于前端UI设计，暂时不需要云开发功能。后续可以根据需要重新添加。

### Q: 如何添加新的页面？
A: 
1. 在 `miniprogram/pages/` 下创建新目录
2. 在 `miniprogram/app.json` 的 `pages` 数组中添加页面路径
3. 创建对应的 `.js`, `.wxml`, `.wxss`, `.json` 文件

### Q: 如何自定义颜色？
A: 修改 `miniprogram/styles/variables.wxss` 中的CSS变量

## 📞 获取帮助

如果遇到其他问题，请：
1. 检查微信开发者工具的控制台错误信息
2. 确认微信开发者工具版本是否为最新
3. 检查项目的基础库版本设置

---

**最后更新**：2025-01-27
**适用版本**：微信小程序基础库 2.20.1+
