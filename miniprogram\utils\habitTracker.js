// 《能量星球》习惯追踪系统
// 专注于长期习惯养成和行为模式分析

const habitTracker = {
  
  // 习惯强度等级
  HABIT_STRENGTH: {
    TRYING: 'trying',           // 尝试阶段 (1-6天)
    FORMING: 'forming',         // 形成阶段 (7-20天)
    DEVELOPING: 'developing',   // 发展阶段 (21-65天)
    ESTABLISHED: 'established'  // 建立阶段 (66天+)
  },

  // 习惯类型
  HABIT_TYPES: {
    HYGIENE: 'hygiene',                    // 卫生习惯
    ORGANIZATION: 'organization',          // 整理习惯
    TIME_MANAGEMENT: 'time_management',    // 时间管理
    HELPING: 'helping',                    // 助人习惯
    GRATITUDE: 'gratitude',               // 感恩习惯
    COMMUNICATION: 'communication',        // 沟通习惯
    CARING: 'caring',                     // 关爱习惯
    POLITENESS: 'politeness',             // 礼貌习惯
    COOPERATION: 'cooperation',           // 合作习惯
    ENVIRONMENTAL: 'environmental',        // 环保习惯
    CIVIC_RESPONSIBILITY: 'civic_responsibility', // 公民责任
    CULTURAL: 'cultural'                  // 文化学习
  },

  // 记录习惯完成
  recordHabitCompletion(habitType, taskId) {
    const today = new Date().toDateString();
    const habitKey = `habit_${habitType}`;
    
    // 获取习惯记录
    let habitRecord = wx.getStorageSync(habitKey) || {
      habitType: habitType,
      startDate: today,
      completionDates: [],
      currentStreak: 0,
      longestStreak: 0,
      totalCompletions: 0
    };

    // 检查今天是否已经记录
    if (!habitRecord.completionDates.includes(today)) {
      habitRecord.completionDates.push(today);
      habitRecord.totalCompletions++;
      
      // 计算连续天数
      this.updateStreakData(habitRecord, today);
      
      // 保存记录
      wx.setStorageSync(habitKey, habitRecord);
      
      // 检查是否达到里程碑
      this.checkMilestones(habitType, habitRecord);
    }

    return habitRecord;
  },

  // 更新连续天数数据
  updateStreakData(habitRecord, today) {
    const todayDate = new Date(today);
    const yesterdayDate = new Date(todayDate.getTime() - 24 * 60 * 60 * 1000);
    const yesterday = yesterdayDate.toDateString();

    if (habitRecord.completionDates.includes(yesterday)) {
      // 连续天数+1
      habitRecord.currentStreak++;
    } else {
      // 重新开始计算连续天数
      habitRecord.currentStreak = 1;
    }

    // 更新最长连续记录
    if (habitRecord.currentStreak > habitRecord.longestStreak) {
      habitRecord.longestStreak = habitRecord.currentStreak;
    }
  },

  // 检查里程碑成就
  checkMilestones(habitType, habitRecord) {
    const milestones = [7, 21, 66, 100, 365]; // 重要的习惯养成节点
    
    milestones.forEach(milestone => {
      if (habitRecord.currentStreak === milestone) {
        this.triggerMilestoneAchievement(habitType, milestone);
      }
    });
  },

  // 触发里程碑成就
  triggerMilestoneAchievement(habitType, days) {
    const achievementKey = `milestone_${habitType}_${days}`;
    const existingAchievement = wx.getStorageSync(achievementKey);
    
    if (!existingAchievement) {
      const achievement = {
        id: achievementKey,
        habitType: habitType,
        milestone: days,
        achievedAt: new Date().toISOString(),
        title: this.getMilestoneTitle(habitType, days),
        description: this.getMilestoneDescription(habitType, days),
        badge: this.getMilestoneBadge(habitType, days)
      };
      
      // 保存成就
      wx.setStorageSync(achievementKey, achievement);
      
      // 添加到成就列表
      this.addToAchievementsList(achievement);
      
      // 显示成就通知
      this.showAchievementNotification(achievement);
    }
  },

  // 获取里程碑标题
  getMilestoneTitle(habitType, days) {
    const habitNames = {
      hygiene: '卫生习惯',
      organization: '整理习惯',
      time_management: '时间管理',
      helping: '助人习惯',
      gratitude: '感恩习惯',
      communication: '沟通习惯',
      caring: '关爱习惯',
      politeness: '礼貌习惯',
      cooperation: '合作习惯',
      environmental: '环保习惯',
      civic_responsibility: '公民责任',
      cultural: '文化学习'
    };

    const milestoneNames = {
      7: '初心坚持',
      21: '习惯萌芽',
      66: '行为自然',
      100: '百日成就',
      365: '年度大师'
    };

    return `${habitNames[habitType]}${milestoneNames[days]}`;
  },

  // 获取里程碑描述
  getMilestoneDescription(habitType, days) {
    if (days === 7) {
      return `坚持${days}天，迈出了习惯养成的第一步！`;
    } else if (days === 21) {
      return `坚持${days}天，习惯开始在大脑中形成神经通路！`;
    } else if (days === 66) {
      return `坚持${days}天，这个行为已经成为你的自然反应！`;
    } else if (days === 100) {
      return `坚持${days}天，你已经是这个领域的小专家了！`;
    } else if (days === 365) {
      return `坚持${days}天，一年的坚持让你成为真正的大师！`;
    }
    return `坚持${days}天，继续保持这个好习惯！`;
  },

  // 获取里程碑徽章
  getMilestoneBadge(habitType, days) {
    const badges = {
      7: '🌱',
      21: '🌿',
      66: '🌳',
      100: '🏆',
      365: '👑'
    };
    return badges[days] || '⭐';
  },

  // 添加到成就列表
  addToAchievementsList(achievement) {
    let achievements = wx.getStorageSync('achievements') || [];
    achievements.push(achievement);
    wx.setStorageSync('achievements', achievements);
  },

  // 显示成就通知
  showAchievementNotification(achievement) {
    // 这里可以触发页面的成就动画或通知
    console.log('🎉 新成就解锁:', achievement.title);
  },

  // 获取习惯强度
  getHabitStrength(habitType) {
    const habitKey = `habit_${habitType}`;
    const habitRecord = wx.getStorageSync(habitKey);
    
    if (!habitRecord) {
      return this.HABIT_STRENGTH.TRYING;
    }

    const streak = habitRecord.currentStreak;
    
    if (streak >= 66) {
      return this.HABIT_STRENGTH.ESTABLISHED;
    } else if (streak >= 21) {
      return this.HABIT_STRENGTH.DEVELOPING;
    } else if (streak >= 7) {
      return this.HABIT_STRENGTH.FORMING;
    } else {
      return this.HABIT_STRENGTH.TRYING;
    }
  },

  // 获取所有习惯统计
  getAllHabitsStats() {
    const habitTypes = Object.values(this.HABIT_TYPES);
    const stats = {};

    habitTypes.forEach(habitType => {
      const habitKey = `habit_${habitType}`;
      const habitRecord = wx.getStorageSync(habitKey);
      
      if (habitRecord) {
        stats[habitType] = {
          currentStreak: habitRecord.currentStreak,
          longestStreak: habitRecord.longestStreak,
          totalCompletions: habitRecord.totalCompletions,
          strength: this.getHabitStrength(habitType),
          startDate: habitRecord.startDate
        };
      }
    });

    return stats;
  },

  // 获取习惯养成建议
  getHabitSuggestions(userData = {}) {
    const allStats = this.getAllHabitsStats();
    const suggestions = [];

    // 分析薄弱的习惯类型
    const weakHabits = Object.keys(allStats).filter(habitType => {
      const stats = allStats[habitType];
      return stats.currentStreak < 7 || stats.strength === this.HABIT_STRENGTH.TRYING;
    });

    // 为薄弱习惯提供建议
    weakHabits.forEach(habitType => {
      suggestions.push({
        habitType: habitType,
        suggestion: this.getSpecificHabitSuggestion(habitType),
        priority: this.getHabitPriority(habitType, userData)
      });
    });

    // 按优先级排序
    suggestions.sort((a, b) => b.priority - a.priority);

    return suggestions.slice(0, 3); // 返回前3个建议
  },

  // 获取特定习惯建议
  getSpecificHabitSuggestion(habitType) {
    const suggestions = {
      hygiene: '建议设置固定的刷牙时间，比如早上起床后和晚上睡前',
      organization: '可以从整理一个小区域开始，比如书桌或玩具箱',
      time_management: '尝试使用闹钟或提醒，建立固定的作息时间',
      helping: '从小事做起，比如帮忙摆放餐具或收拾玩具',
      gratitude: '每天睡前想一想今天要感谢的人或事',
      communication: '主动和家人分享今天的见闻和感受',
      caring: '观察身边需要帮助的人，主动伸出援手',
      politeness: '练习在日常对话中使用"请"、"谢谢"、"对不起"',
      cooperation: '积极参与集体活动，学会倾听和配合他人',
      environmental: '从身边的小事做起，比如随手关灯、垃圾分类',
      civic_responsibility: '了解并遵守公共场所的规则',
      cultural: '可以从了解传统节日的由来和习俗开始'
    };

    return suggestions[habitType] || '坚持每天练习，养成良好习惯';
  },

  // 获取习惯优先级
  getHabitPriority(habitType, userData) {
    // 基础优先级
    const basePriority = {
      hygiene: 10,
      time_management: 9,
      organization: 8,
      politeness: 8,
      helping: 7,
      gratitude: 7,
      communication: 6,
      caring: 6,
      cooperation: 5,
      environmental: 5,
      civic_responsibility: 4,
      cultural: 3
    };

    let priority = basePriority[habitType] || 5;

    // 根据用户年龄调整优先级
    const age = userData.age || 8;
    if (age < 6) {
      // 小年龄更注重基础生活习惯
      if (['hygiene', 'organization', 'politeness'].includes(habitType)) {
        priority += 2;
      }
    } else if (age > 9) {
      // 大年龄更注重社会责任
      if (['civic_responsibility', 'environmental', 'cultural'].includes(habitType)) {
        priority += 2;
      }
    }

    return priority;
  }
};

module.exports = {
  habitTracker
};
