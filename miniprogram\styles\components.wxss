/* 《能量星球》组件样式库 */

/* 引入基础样式 */
@import "base.wxss";

/* ==================== 按钮组件 ==================== */

/* 按钮基础样式 */
.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  min-height: var(--button-height-md);
  min-width: var(--touch-target-comfortable);
  padding: var(--spacing-sm) var(--spacing-lg);
  border: none;
  border-radius: var(--radius-lg);
  font-size: var(--font-size-md);
  font-weight: var(--font-weight-semibold);
  text-align: center;
  transition: all var(--duration-normal) var(--ease-out);
  position: relative;
  overflow: hidden;
}

.btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(255, 255, 255, 0.1);
  opacity: 0;
  transition: opacity var(--duration-fast) var(--ease-out);
}

.btn:active::before {
  opacity: 1;
}

.btn:active {
  transform: scale(0.95);
}

/* 主要按钮 - 能量黄 */
.btn-primary {
  background: var(--gradient-energy);
  color: var(--space-deep);
  box-shadow: 0 8rpx 24rpx var(--glow-energy);
}

.btn-primary:hover {
  box-shadow: 0 12rpx 32rpx var(--glow-energy);
  transform: translateY(-2rpx);
}

/* 次要按钮 - 智慧蓝 */
.btn-secondary {
  background: var(--gradient-wisdom);
  color: var(--star-white);
  box-shadow: 0 8rpx 24rpx var(--glow-wisdom);
}

.btn-secondary:hover {
  box-shadow: 0 12rpx 32rpx var(--glow-wisdom);
  transform: translateY(-2rpx);
}

/* 爱心按钮 - 爱心红 */
.btn-love {
  background: var(--gradient-love);
  color: var(--star-white);
  box-shadow: 0 8rpx 24rpx var(--glow-love);
}

.btn-love:hover {
  box-shadow: 0 12rpx 32rpx var(--glow-love);
  transform: translateY(-2rpx);
}

/* 成功按钮 - 成就绿 */
.btn-success {
  background: var(--gradient-success);
  color: var(--space-deep);
  box-shadow: 0 8rpx 24rpx var(--glow-success);
}

.btn-success:hover {
  box-shadow: 0 12rpx 32rpx var(--glow-success);
  transform: translateY(-2rpx);
}

/* 幽灵按钮 */
.btn-ghost {
  background: transparent;
  color: var(--star-white);
  border: 2rpx solid var(--star-white);
  box-shadow: none;
}

.btn-ghost:hover {
  background: var(--star-white-light);
}

/* 按钮尺寸变体 */
.btn-sm {
  min-height: var(--button-height-sm);
  padding: var(--spacing-xs) var(--spacing-md);
  font-size: var(--font-size-sm);
}

.btn-lg {
  min-height: var(--button-height-lg);
  padding: var(--spacing-md) var(--spacing-xl);
  font-size: var(--font-size-lg);
}

/* 圆形按钮 */
.btn-round {
  border-radius: var(--radius-round);
  width: var(--touch-target-comfortable);
  height: var(--touch-target-comfortable);
  padding: 0;
}

/* 禁用状态 */
.btn-disabled {
  opacity: 0.5;
  transform: none !important;
  box-shadow: none !important;
}

/* ==================== 卡片组件 ==================== */

.card {
  background: var(--glass-background);
  backdrop-filter: var(--glass-backdrop-filter);
  border: 2rpx solid var(--glass-border);
  border-radius: var(--radius-lg);
  padding: var(--spacing-lg);
  box-shadow: 0 8rpx 32rpx var(--shadow-soft);
  transition: all var(--duration-normal) var(--ease-out);
}

.card:hover {
  transform: translateY(-4rpx);
  box-shadow: 0 16rpx 48rpx var(--shadow-medium);
}

/* 卡片变体 */
.card-energy {
  border-color: var(--energy-yellow-light);
  box-shadow: 0 8rpx 32rpx var(--glow-energy);
}

.card-wisdom {
  border-color: var(--wisdom-blue-light);
  box-shadow: 0 8rpx 32rpx var(--glow-wisdom);
}

.card-love {
  border-color: var(--love-red-light);
  box-shadow: 0 8rpx 32rpx var(--glow-love);
}

.card-success {
  border-color: var(--success-green-light);
  box-shadow: 0 8rpx 32rpx var(--glow-success);
}

/* 卡片头部 */
.card-header {
  padding-bottom: var(--spacing-md);
  margin-bottom: var(--spacing-md);
  border-bottom: 1rpx solid var(--glass-border);
}

/* 卡片内容 */
.card-body {
  flex: 1;
}

/* 卡片底部 */
.card-footer {
  padding-top: var(--spacing-md);
  margin-top: var(--spacing-md);
  border-top: 1rpx solid var(--glass-border);
}

/* ==================== 能量条组件 ==================== */

.energy-bar {
  width: 100%;
  height: 16rpx;
  background: var(--glass-background);
  border-radius: var(--radius-sm);
  overflow: hidden;
  position: relative;
  border: 1rpx solid var(--glass-border);
}

.energy-bar-fill {
  height: 100%;
  border-radius: var(--radius-sm);
  transition: width var(--duration-slow) var(--ease-out);
  position: relative;
  overflow: hidden;
}

.energy-bar-fill::after {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
  animation: energy-shine 2s infinite;
}

@keyframes energy-shine {
  0% { left: -100%; }
  100% { left: 100%; }
}

/* 能量条变体 */
.energy-bar-wisdom .energy-bar-fill {
  background: var(--gradient-wisdom);
}

.energy-bar-love .energy-bar-fill {
  background: var(--gradient-love);
}

.energy-bar-energy .energy-bar-fill {
  background: var(--gradient-energy);
}

/* 能量条尺寸 */
.energy-bar-sm {
  height: 12rpx;
}

.energy-bar-lg {
  height: 24rpx;
}

/* ==================== 头像组件 ==================== */

.avatar {
  display: inline-block;
  width: var(--avatar-md);
  height: var(--avatar-md);
  border-radius: var(--radius-round);
  overflow: hidden;
  background: var(--glass-background);
  border: 2rpx solid var(--glass-border);
  position: relative;
}

.avatar-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

/* 头像尺寸 */
.avatar-sm {
  width: var(--avatar-sm);
  height: var(--avatar-sm);
}

.avatar-lg {
  width: var(--avatar-lg);
  height: var(--avatar-lg);
}

.avatar-xl {
  width: var(--avatar-xl);
  height: var(--avatar-xl);
}

/* 头像状态指示器 */
.avatar-status {
  position: absolute;
  bottom: 0;
  right: 0;
  width: 20rpx;
  height: 20rpx;
  border-radius: var(--radius-round);
  border: 2rpx solid var(--star-white);
}

.avatar-status-online {
  background: var(--success-green);
}

.avatar-status-offline {
  background: var(--neutral-gray);
}

/* ==================== 徽章组件 ==================== */

.badge {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  min-width: 40rpx;
  height: 40rpx;
  padding: 0 var(--spacing-xs);
  background: var(--love-red);
  color: var(--star-white);
  font-size: var(--font-size-xs);
  font-weight: var(--font-weight-semibold);
  border-radius: var(--radius-pill);
  position: relative;
}

/* 徽章变体 */
.badge-energy {
  background: var(--energy-yellow);
  color: var(--space-deep);
}

.badge-wisdom {
  background: var(--wisdom-blue);
}

.badge-success {
  background: var(--success-green);
  color: var(--space-deep);
}

/* 徽章位置 */
.badge-absolute {
  position: absolute;
  top: -8rpx;
  right: -8rpx;
  z-index: var(--z-index-dropdown);
}

/* ==================== 图标组件 ==================== */

.icon {
  display: inline-block;
  width: var(--icon-md);
  height: var(--icon-md);
  fill: currentColor;
  vertical-align: middle;
}

/* 图标尺寸 */
.icon-xs { width: var(--icon-xs); height: var(--icon-xs); }
.icon-sm { width: var(--icon-sm); height: var(--icon-sm); }
.icon-lg { width: var(--icon-lg); height: var(--icon-lg); }
.icon-xl { width: var(--icon-xl); height: var(--icon-xl); }
