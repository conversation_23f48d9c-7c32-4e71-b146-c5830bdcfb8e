<!--《能量星球》地球指挥部 - 家长中心主界面-->
<view class="page command-center">
  <!-- HUD 指挥部抬头显示器 -->
  <view class="command-hud">
    <!-- 指挥部标题 -->
    <view class="command-title">
      <view class="title-glow"></view>
      <text class="title-text">地球指挥部</text>
      <text class="subtitle-text">Earth Command Center</text>
    </view>
  </view>

  <!-- 宇宙背景 -->
  <view class="space-background">
    <!-- 星空层 -->
    <view class="stars-layer">
      <view class="star star-1"></view>
      <view class="star star-2"></view>
      <view class="star star-3"></view>
      <view class="star star-4"></view>
    </view>
    <!-- 数据流动层 -->
    <view class="data-streams">
      <view class="data-stream stream-1"></view>
      <view class="data-stream stream-2"></view>
      <view class="data-stream stream-3"></view>
    </view>
  </view>

  <!-- 主控制台区域 -->
  <view class="main-console">
    <!-- 实时监控台 -->
    <view class="console-module monitor" bindtap="onOpenMonitor">
      <view class="module-glow"></view>
      <view class="module-header">
        <text class="module-icon">📊</text>
        <text class="module-title">实时监控台</text>
      </view>
      <view class="module-content">
        <view class="monitor-stats">
          <view class="stat-item">
            <text class="stat-label">今日学习</text>
            <text class="stat-value">{{todayStats.studyTime}}分钟</text>
          </view>
          <view class="stat-item">
            <text class="stat-label">游戏完成</text>
            <text class="stat-value">{{todayStats.gamesCompleted}}个</text>
          </view>
        </view>
        <view class="activity-indicator">
          <view class="activity-bar" style="width: {{activityPercentage}}%"></view>
        </view>
      </view>
    </view>

    <!-- AI分析中心 -->
    <view class="console-module analysis" bindtap="onOpenAnalysis">
      <view class="module-glow ai-glow"></view>
      <view class="module-header">
        <text class="module-icon">🧠</text>
        <text class="module-title">AI分析中心</text>
      </view>
      <view class="module-content">
        <view class="ability-preview">
          <view class="ability-item" wx:for="{{abilityPreview}}" wx:key="name">
            <text class="ability-name">{{item.name}}</text>
            <view class="ability-progress">
              <view class="progress-bar" style="width: {{item.score}}%"></view>
            </view>
          </view>
        </view>
        <view class="ai-status">
          <text class="ai-text">AI分析完成度: {{aiAnalysisProgress}}%</text>
        </view>
      </view>
    </view>

    <!-- 奖励管理系统 -->
    <view class="console-module rewards" bindtap="onOpenRewards">
      <view class="module-glow reward-glow"></view>
      <view class="module-header">
        <text class="module-icon">🎁</text>
        <text class="module-title">奖励管理</text>
      </view>
      <view class="module-content">
        <view class="reward-summary">
          <view class="reward-stat">
            <text class="reward-count">{{rewardStats.active}}</text>
            <text class="reward-label">活跃奖励</text>
          </view>
          <view class="reward-stat">
            <text class="reward-count">{{rewardStats.pending}}</text>
            <text class="reward-label">待兑换</text>
          </view>
        </view>
        <view class="reward-indicator">
          <text class="reward-status">{{rewardStatusText}}</text>
        </view>
      </view>
    </view>

    <!-- 设置控制中心 -->
    <view class="console-module settings" bindtap="onOpenSettings">
      <view class="module-glow settings-glow"></view>
      <view class="module-header">
        <text class="module-icon">⚙️</text>
        <text class="module-title">设置中心</text>
      </view>
      <view class="module-content">
        <view class="settings-preview">
          <view class="setting-item">
            <text class="setting-name">学习时间限制</text>
            <text class="setting-value">{{settings.dailyLimit}}分钟</text>
          </view>
          <view class="setting-item">
            <text class="setting-name">通知提醒</text>
            <text class="setting-value">{{settings.notifications ? '开启' : '关闭'}}</text>
          </view>
        </view>
      </view>
    </view>

    <!-- 亲子任务中心 - 星际协作指挥台 -->
    <view class="console-module cooperation" bindtap="onOpenCooperation">
      <view class="module-glow cooperation-glow"></view>
      <view class="module-header">
        <text class="module-icon">🤝</text>
        <text class="module-title">协作指挥台</text>
      </view>
      <view class="module-content">
        <view class="cooperation-preview">
          <view class="task-summary">
            <view class="task-stat">
              <text class="task-count">{{cooperationStats.active}}</text>
              <text class="task-label">进行中</text>
            </view>
            <view class="task-stat">
              <text class="task-count">{{cooperationStats.completed}}</text>
              <text class="task-label">已完成</text>
            </view>
          </view>
          <view class="cooperation-animation">
            <view class="planet planet-parent"></view>
            <view class="planet planet-child"></view>
            <view class="connection-line"></view>
          </view>
        </view>
        <view class="cooperation-status">
          <text class="status-text">{{cooperationStatusText}}</text>
        </view>
      </view>
    </view>

    <!-- 学习报告生成 - 舰长成长档案 -->
    <view class="console-module reports" bindtap="onOpenReports">
      <view class="module-glow reports-glow"></view>
      <view class="module-header">
        <text class="module-icon">📈</text>
        <text class="module-title">成长档案</text>
      </view>
      <view class="module-content">
        <view class="reports-preview">
          <view class="chart-container">
            <view class="chart-line" wx:for="{{chartData}}" wx:key="index"
                  style="height: {{item}}%; animation-delay: {{index * 0.1}}s"></view>
          </view>
          <view class="report-summary">
            <view class="summary-item">
              <text class="summary-label">本周学习</text>
              <text class="summary-value">{{weeklyStats.totalTime}}小时</text>
            </view>
            <view class="summary-item">
              <text class="summary-label">能力提升</text>
              <text class="summary-value">+{{weeklyStats.improvement}}%</text>
            </view>
          </view>
        </view>
        <view class="data-flow">
          <view class="data-particle" wx:for="{{5}}" wx:key="*this"></view>
        </view>
      </view>
    </view>
  </view>

  <!-- 底部控制栏 -->
  <view class="bottom-controls">
    <view class="control-button back {{backAnimating ? 'animating' : ''}}" bindtap="onGoBack">
      <!-- 超级火箭引擎效果 -->
      <view class="rocket-engine">
        <!-- 主火焰层 -->
        <view class="flame-layer main-flame">
          <view class="flame-particle" wx:for="{{12}}" wx:key="*this"></view>
        </view>
        <!-- 次火焰层 -->
        <view class="flame-layer sub-flame">
          <view class="flame-particle" wx:for="{{8}}" wx:key="*this"></view>
        </view>
        <!-- 离子推进器 -->
        <view class="ion-thruster">
          <view class="ion-beam"></view>
          <view class="ion-core"></view>
        </view>
        <!-- 震动波 -->
        <view class="shock-wave"></view>
      </view>

      <!-- 空间扭曲效果 -->
      <view class="space-distortion">
        <view class="distortion-ring" wx:for="{{3}}" wx:key="*this"></view>
      </view>

      <!-- 按钮内容 -->
      <view class="button-content">
        <text class="control-icon rocket-icon">🚀</text>
        <text class="control-text">返回舰桥</text>
      </view>

      <!-- 能量护盾 -->
      <view class="energy-shield"></view>
    </view>

    <view class="control-button refresh {{refreshAnimating ? 'animating' : ''}}" bindtap="onRefreshData">
      <!-- 量子数据传输效果 -->
      <view class="quantum-system">
        <!-- 全息扫描网格 -->
        <view class="hologram-grid">
          <view class="grid-line horizontal" wx:for="{{5}}" wx:key="*this"></view>
          <view class="grid-line vertical" wx:for="{{5}}" wx:key="*this"></view>
        </view>

        <!-- 数据流瀑布 -->
        <view class="data-waterfall">
          <view class="data-stream" wx:for="{{6}}" wx:key="*this">
            <view class="data-bit" wx:for="{{8}}" wx:key="*this">{{item % 2}}</view>
          </view>
        </view>

        <!-- 量子粒子 -->
        <view class="quantum-particles">
          <view class="quantum-dot" wx:for="{{15}}" wx:key="*this"></view>
        </view>

        <!-- 能量脉冲核心 -->
        <view class="energy-core">
          <view class="core-ring" wx:for="{{4}}" wx:key="*this"></view>
        </view>
      </view>

      <!-- 按钮内容 -->
      <view class="button-content">
        <text class="control-icon refresh-icon">🔄</text>
        <text class="control-text">刷新数据</text>
      </view>

      <!-- 量子场 -->
      <view class="quantum-field"></view>
    </view>
  </view>
</view>
